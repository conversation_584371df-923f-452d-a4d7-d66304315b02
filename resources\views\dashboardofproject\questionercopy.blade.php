@extends('layouts/Admin')

@section('title','Questioner Copy')

@section('contents')
<!-- Debug Information -->
@if(config('app.debug'))
<div class="mb-4 p-3 bg-blue-100 border border-blue-300 rounded-lg text-sm">
    <strong>Debug Info:</strong>
    Questions Count: {{ isset($questions) ? count($questions) : 'Not set' }} |
    Route: {{ request()->route()->getName() ?? 'No route name' }}
</div>
@endif
<div class="p-4 md:p-8 min-h-screen transition-all w-full bg-white">
    <div class="container mx-auto max-w-6xl">
        <!-- Alert Messages -->
        @if(session('success'))
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 rounded shadow" role="alert">
                <p>{{ session('success') }}</p>
            </div>
        @endif
        
        @if(session('error'))
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded shadow" role="alert">
                <p>{{ session('error') }}</p>
            </div>
        @endif

        <!-- Page Header -->
        <div class="flex flex-col md:flex-row justify-between items-center mb-8">
            <div class="flex items-center mb-4 md:mb-0">
                <div class="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center shadow-lg transform hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-question-circle text-gray-700 text-xl"></i>
                </div>
                <h1 class="text-2xl md:text-3xl font-bold text-gray-800 mr-4">{{ translateText('د پوښتنو مدیریت') }}</h1>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('questioner.seed') }}" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 shadow-md transition-all duration-300 flex items-center">
                    <i class="fas fa-seedling mr-2"></i>
                    {{ translateText('د پوښتنو سیډر') }}
                </a>
                <button onclick="showForm()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 shadow-md transition-all duration-300 flex items-center">
                    <i class="fas fa-plus-circle mr-2"></i>
                    {{ translateText('نوې پوښتنه') }}
                </button>
            </div>
        </div>

        <!-- د پوښتنې د اضافه کولو فورم -->
        <div id="questionForm" class="bg-white rounded-2xl shadow-xl p-6 mb-8 border-0" style="display: none;">
            <h3 class="text-xl font-bold mb-4 text-gray-800 border-b-2 border-gray-300 pb-2 flex items-center">
                <i class="fas fa-question-circle mr-2 text-gray-600"></i>
                <span id="formTitle">{{ translateText('نوې پوښتنه اضافه کړئ') }}</span>
            </h3>
            <form action="{{ route('questioner.store') }}" method="POST" id="questionerForm">
                @csrf
                <div id="methodField"></div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="col-span-2">
                        <label class="block text-gray-700 mb-2">{{ translateText('پوښتنه') }}</label>
                        <textarea name="Q_Discription" id="Q_Discription" rows="3" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required></textarea>
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-2">{{ translateText('د پوښتنې شمیره') }}</label>
                        <input type="number" name="Question_No" id="Question_No" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                    </div>
                    <div class="col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-gray-700 mb-2">{{ translateText('الف') }}</label>
                            <input type="text" name="A" id="A" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" value="هیڅ نه">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">{{ translateText('ب') }}</label>
                            <input type="text" name="B" id="B" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" value="لږه اندازه">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">{{ translateText('ج') }}</label>
                            <input type="text" name="C" id="C" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" value="لږه ډېره اندازه">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">{{ translateText('د') }}</label>
                            <input type="text" name="D" id="D" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" value="ډېره اندازه">
                        </div>
                    </div>
                </div>
                <div class="mt-6 flex justify-end">
                    <button type="button" onclick="hideForm()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 mr-2">
                        {{ translateText('لغوه کول') }}
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        <span id="submitButtonText">{{ translateText('ثبتول') }}</span>
                    </button>
                </div>
            </form>
        </div>

        <!-- د پوښتنو لیست -->
        <div class="bg-white rounded-2xl shadow-xl p-4 border-0">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-gray-800 pb-2 flex items-center border-0">
                    <i class="fas fa-list-ul mr-2 text-gray-600"></i>
                    {{ translateText('ثبت شوې پوښتنې') }}
                </h3>
                <div class="flex space-x-2">
                    <span class="text-sm text-gray-600">{{ translateText('ټول پوښتنې') }}: {{ $questions->total() }}</span>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="table-fixed w-full text-xs">
                    <thead>
                        <tr class="bg-white text-gray-800 border border-gray-800">
                            <th class="py-2 px-1 text-center w-8 border-r border-gray-800">{{ translateText('شمېره') }}</th>
                            <th class="py-2 px-1 text-center w-16 border-r border-gray-800">{{ translateText('د پوښتنې شمیره') }}</th>
                            <th class="py-2 px-1 text-center w-32 border-r border-gray-800">{{ translateText('پوښتنه') }}</th>
                            <th class="py-2 px-1 text-center w-16 border-r border-gray-800">{{ translateText('الف') }}</th>
                            <th class="py-2 px-1 text-center w-16 border-r border-gray-800">{{ translateText('ب') }}</th>
                            <th class="py-2 px-1 text-center w-16 border-r border-gray-800">{{ translateText('ج') }}</th>
                            <th class="py-2 px-1 text-center w-16 border-r border-gray-800">{{ translateText('د') }}</th>
                            <th class="py-2 px-1 text-center w-12">{{ translateText('کړنې') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if(isset($questions) && count($questions) > 0)
                            @foreach($questions as $question)
                                <tr class="border-b hover:bg-gray-100">
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $loop->iteration }}</td>
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $question->Question_No ?? 'N/A' }}</td>
                                    <td class="py-2 px-1 text-right truncate border-r border-gray-200">
                                        <div class="tooltip" title="{{ $question->Q_Discription }}">
                                            {{ \Illuminate\Support\Str::limit($question->Q_Discription, 50) }}
                                        </div>
                                    </td>
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $question->A }}</td>
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $question->B }}</td>
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $question->C }}</td>
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $question->D }}</td>
                                    <td class="py-2 px-1 text-center">
                                        <div class="flex justify-center">
                                            <button type="button" onclick="editQuestion(
                                                '{{ $question->Q_Id }}',
                                                '{{ addslashes($question->Q_Discription) }}',
                                                '{{ $question->Question_No }}',
                                                '{{ addslashes($question->A) }}',
                                                '{{ addslashes($question->B) }}',
                                                '{{ addslashes($question->C) }}',
                                                '{{ addslashes($question->D) }}'
                                            )" class="p-1 bg-white text-blue-600 rounded shadow-sm hover:bg-blue-100 transition-all duration-300 mx-0.5 border-0">
                                                <i class="fas fa-edit text-xs"></i>
                                            </button>
                                            <form action="{{ route('questioner.destroy', $question->Q_Id) }}" method="POST" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="p-1 bg-white text-red-600 rounded shadow-sm hover:bg-red-100 transition-all duration-300 mx-0.5 border-0" onclick="return confirm('{{ translateText('آیا ډاډه یاست چې دا پوښتنه له منځه یوسئ؟') }}')">
                                                    <i class="fas fa-trash text-xs"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="8" class="py-6 text-center text-gray-500">{{ translateText('هیڅ پوښتنه شتون نه لري') }}</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-4 flex justify-center">
                @if(isset($questions))
                    {{ $questions->links() }}
                @endif
            </div>
        </div>
    </div>
</div>

<script>
    function showForm() {
        document.getElementById('questionForm').style.display = 'block';
        document.getElementById('formTitle').innerText = "{{ translateText('نوې پوښتنه اضافه کړئ') }}";
        document.getElementById('submitButtonText').innerText = "{{ translateText('ثبتول') }}";
        document.getElementById('questionerForm').action = "{{ route('questioner.store') }}";
        document.getElementById('methodField').innerHTML = '';
        
        // Clear form fields
        document.getElementById('Q_Discription').value = '';
        document.getElementById('Question_No').value = '';
        document.getElementById('A').value = 'هیڅ نه';
        document.getElementById('B').value = 'لږه اندازه';
        document.getElementById('C').value = 'لږه ډېره اندازه';
        document.getElementById('D').value = 'ډېره اندازه';
        
        // Scroll to form
        document.getElementById('questionForm').scrollIntoView({behavior: 'smooth'});
    }
    
    function hideForm() {
        document.getElementById('questionForm').style.display = 'none';
    }
    
    function editQuestion(id, Q_Discription, question_no, A, B, C, D) {
        // Show form and update title
        document.getElementById('questionForm').style.display = 'block';
        document.getElementById('formTitle').innerText = "{{ translateText('پوښتنه تازه کړئ') }}";
        document.getElementById('submitButtonText').innerText = "{{ translateText('تازه کول') }}";

        // Update form action and method
        document.getElementById('questionerForm').action = "{{ url('questioner') }}/" + id;
        document.getElementById('methodField').innerHTML = '@method("PUT")';

        // Fill form with question data
        document.getElementById('Q_Discription').value = Q_Discription;
        document.getElementById('Question_No').value = question_no;
        document.getElementById('A').value = A;
        document.getElementById('B').value = B;
        document.getElementById('C').value = C;
        document.getElementById('D').value = D;
        
        // Scroll to form
        document.getElementById('questionForm').scrollIntoView({behavior: 'smooth'});
    }
</script>

<style>
    .tooltip {
        position: relative;
        display: inline-block;
        width: 100%;
    }
    
    .tooltip:hover::after {
        content: attr(title);
        position: absolute;
        left: 0;
        top: 100%;
        z-index: 1;
        background-color: #333;
        color: white;
        padding: 5px 10px;
        border-radius: 5px;
        white-space: normal;
        width: 250px;
        font-size: 12px;
        text-align: right;
    }
</style>
@endsection












