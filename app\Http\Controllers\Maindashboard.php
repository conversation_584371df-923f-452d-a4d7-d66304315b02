<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\DoctorModel;
use App\Models\PatientModel;
use App\Models\FeedbackModel;
use App\Models\QuestionerModel;
use App\Models\BookModel;
use App\Models\NewsModel;
use App\Models\VideoModel;
use App\Models\ArticalModel;
use App\Models\ArticleModel;
use Illuminate\Support\Facades\Log;

class Maindashboard extends Controller
{
    /**
     * Display the dashboard index.
     */
    public function DashboardIndex()
    {
        try {
            // Test database connection first
            \DB::connection()->getPdo();

            // Count records for dashboard statistics with null checks
            $patientCount = PatientModel::count() ?? 0;
            $patientsCount = $patientCount; // Add this line for backward compatibility
            $doctorCount = DoctorModel::count() ?? 0;
            $commentCount = FeedbackModel::count() ?? 0;
            $feedbackCount = $commentCount; // Add feedbackCount for the new dashboard
            $newsCount = NewsModel::count() ?? 0;
            $articleCount = ArticalModel::count() ?? 0;
            $videoCount = VideoModel::count() ?? 0;
            $activeUsers = 12; // Mock data for active users

            // Get patient chart data with error handling
            $patientChartData = $this->getPatientChartData();

            // Get question chart data with error handling
            $questionChartData = $this->getQuestionChartData();

            // Ensure questionChartData is always an array
            if (!is_array($questionChartData)) {
                $questionChartData = [];
            }

            // Return the new professional dashboard view
            return view('dashboardofproject.mainpageofdashboard', compact(
                'patientCount',
                'patientsCount', // Add this variable
                'doctorCount',
                'commentCount',
                'feedbackCount',
                'newsCount',
                'articleCount',
                'videoCount',
                'activeUsers',
                'patientChartData',
                'questionChartData'
            ));
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error in DashboardIndex: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());

            // Return view with default values (no error message to prevent frontend error)
            return view('dashboardofproject.mainpageofdashboard', [
                'patientCount' => 0,
                'patientsCount' => 0,
                'doctorCount' => 0,
                'commentCount' => 0,
                'feedbackCount' => 0,
                'newsCount' => 0,
                'articleCount' => 0,
                'videoCount' => 0,
                'activeUsers' => 0,
                'patientChartData' => [
                    'genderData' => ['نارینه' => 0, 'ښځینه' => 0],
                    'ageGroups' => ['۰-۱۸' => 0, '۱۹-۳۵' => 0, '۳۶-۵۰' => 0, '۵۱+' => 0],
                    'monthlyData' => [],
                    'provinceData' => []
                ],
                'questionChartData' => []
            ]);
        }
    }

    /**
     * Display the main page of dashboard.
     */
    public function mainPageOfDashboard()
    {
        try {
            // Count records for dashboard statistics with null checks
            $patientCount = PatientModel::count() ?? 0;
            $patientsCount = $patientCount; // Add this line for backward compatibility
            $doctorCount = DoctorModel::count() ?? 0;
            $commentCount = FeedbackModel::count() ?? 0;
            $feedbackCount = $commentCount; // Add feedbackCount for the new dashboard
            $newsCount = NewsModel::count() ?? 0;
            $articleCount = ArticalModel::count() ?? 0;
            $videoCount = VideoModel::count() ?? 0;
            $activeUsers = 12; // Mock data for active users

            // Get patient chart data
            $patientChartData = $this->getPatientChartData();

            // Get question chart data with error handling
            $questionChartData = $this->getQuestionChartData();

            // Ensure questionChartData is always an array
            if (!is_array($questionChartData)) {
                $questionChartData = [];
            }

            return view('dashboardofproject.mainpageofdashboard', compact(
                'patientCount',
                'patientsCount', // Add this variable
                'doctorCount',
                'commentCount',
                'feedbackCount',
                'newsCount',
                'articleCount',
                'videoCount',
                'activeUsers',
                'patientChartData',
                'questionChartData'
            ));
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error in mainPageOfDashboard: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());

            // Return view with default values (no error message to prevent frontend error)
            return view('dashboardofproject.mainpageofdashboard', [
                'patientCount' => 0,
                'patientsCount' => 0,
                'doctorCount' => 0,
                'commentCount' => 0,
                'feedbackCount' => 0,
                'newsCount' => 0,
                'articleCount' => 0,
                'videoCount' => 0,
                'activeUsers' => 0,
                'patientChartData' => [
                    'genderData' => ['نارینه' => 0, 'ښځینه' => 0],
                    'ageGroups' => ['۰-۱۸' => 0, '۱۹-۳۵' => 0, '۳۶-۵۰' => 0, '۵۱+' => 0],
                    'monthlyData' => [],
                    'provinceData' => []
                ],
                'questionChartData' => []
            ]);
        }
    }

    /**
     * Display the doctors copy.
     */
    public function doctorcopy()
    {
        // Redirect to the doctor dashboard index route
        return redirect()->route('doctord.index');
    }

    /**
     * Display the patients copy.
     */
    public function patientcopy()
    {
        $patients = PatientModel::with('address')->paginate(10);
        $doctors = DoctorModel::all();

        return view('dashboardofproject.patientcopy', compact('patients', 'doctors'));
    }

    /**
     * Display the feedback copy.
     */
    public function feedbackcopy(Request $request)
    {
        try {
            // Start with base query
            $query = FeedbackModel::query();

            // Apply email filter
            if ($request->filled('search')) {
                $query->where('Recever_email', 'like', '%' . $request->search . '%');
            }

            // Apply date filter
            if ($request->filled('date')) {
                $query->whereDate('created_at', $request->date);
            }

            // Apply content filter
            if ($request->filled('content')) {
                $query->where('Contents', 'like', '%' . $request->content . '%');
            }

            // Get filtered feedbacks with pagination
            $feedbacks = $query->orderBy('created_at', 'desc')->paginate(10);

            // Preserve query parameters for pagination
            $feedbacks->appends($request->all());

            // Get patient emails from database
            $patients = PatientModel::whereNotNull('Patient_email')
                ->where('Patient_email', '!=', '')
                ->select('Patient_id', 'Patiet_Name', 'Patient_email')
                ->get();

            // Flash success message if filters are applied
            if ($request->hasAny(['search', 'date', 'content'])) {
                session()->flash('success', translateText('فلټر په بریالیتوب سره تطبیق شو!'));
            }

            return view('dashboardofproject.Feedbackcopy', compact('feedbacks', 'patients'));
        } catch (\Exception $e) {
            \Log::error('Error in feedbackcopy: ' . $e->getMessage());

            // Return with empty data if there's an error
            return view('dashboardofproject.Feedbackcopy', [
                'feedbacks' => collect(),
                'patients' => collect()
            ]);
        }
    }

    /**
     * Display the news dashboard page.
     *
     * @return \Illuminate\View\View
     */
    public function newscopy(Request $request)
    {
        try {
            // Start with base query
            $query = NewsModel::query();

            // Apply search filter (title search)
            if ($request->filled('search')) {
                $query->where('News_Title', 'like', '%' . $request->search . '%');
            }

            // Apply date filter (specific date)
            if ($request->filled('date')) {
                $query->whereDate('created_at', $request->date);
            }

            // Get filtered news with pagination
            $news = $query->orderBy('created_at', 'desc')->paginate(10);

            // Preserve query parameters for pagination
            $news->appends($request->all());

            // Flash success message if filters are applied
            if ($request->hasAny(['search', 'date'])) {
                session()->flash('success', translateText('فلټر په بریالیتوب سره تطبیق شو!'));
            }

            // Get available years from the database
            $years = NewsModel::selectRaw('YEAR(created_at) as year')
                            ->distinct()
                            ->orderBy('year', 'desc')
                            ->pluck('year')
                            ->toArray();

            // Month names for display
            $monthNames = [
                '1' => translateText('جنوري'),
                '2' => translateText('فبروري'),
                '3' => translateText('مارچ'),
                '4' => translateText('اپریل'),
                '5' => translateText('می'),
                '6' => translateText('جون'),
                '7' => translateText('جولای'),
                '8' => translateText('اګست'),
                '9' => translateText('سپتمبر'),
                '10' => translateText('اکتوبر'),
                '11' => translateText('نومبر'),
                '12' => translateText('دسمبر')
            ];

            return view('dashboardofproject.newscopy', compact('news', 'years', 'monthNames'));
        } catch (\Exception $e) {
            \Log::error('Error in newscopy: ' . $e->getMessage());

            // Return with empty data if there's an error
            return view('dashboardofproject.newscopy', [
                'news' => collect(),
                'years' => [],
                'monthNames' => []
            ]);
        }
    }

    /**
     * Show the questioner copy page.
     *
     * @return \Illuminate\View\View
     */
    public function questionercopy()
    {
        // Get all questions
        $questions = QuestionerModel::paginate(10);

        return view('dashboardofproject.questionercopy', compact('questions'));
    }

    /**
     * Display the video management page.
     *
     * @return \Illuminate\View\View
     */
    public function videocopy()
    {
        $videos = VideoModel::all();
        return view('dashboardofproject.videocopy', compact('videos'));
    }

    /**
     * Display the book copy.
     */
    public function bookcopy()
    {
        $books = ArticalModel::paginate(10);

        return view('dashboardofproject.bookcopy', compact('books'));
    }

    /**
     * Get patient chart data for dashboard
     */
    private function getPatientChartData()
    {
        try {
            // Get gender distribution
            $genderData = PatientModel::selectRaw('Patient_Gender, COUNT(*) as count')
                ->groupBy('Patient_Gender')
                ->pluck('count', 'Patient_Gender')
                ->toArray();

            // Convert to Pashto labels
            $genderLabels = [
                'Male' => 'نارینه',
                'Female' => 'ښځینه',
                'male' => 'نارینه',
                'female' => 'ښځینه',
                'نارینه' => 'نارینه',
                'ښځینه' => 'ښځینه'
            ];

            $processedGenderData = [];
            foreach ($genderData as $gender => $count) {
                $label = $genderLabels[$gender] ?? $gender;
                $processedGenderData[$label] = $count;
            }

            // Get age groups
            $patients = PatientModel::all();
            $ageGroups = [
                '۰-۱۸' => 0,
                '۱۹-۳۵' => 0,
                '۳۶-۵۰' => 0,
                '۵۱+' => 0
            ];

            foreach ($patients as $patient) {
                $age = $patient->Patient_Age;
                if ($age <= 18) {
                    $ageGroups['۰-۱۸']++;
                } elseif ($age <= 35) {
                    $ageGroups['۱۹-۳۵']++;
                } elseif ($age <= 50) {
                    $ageGroups['۳۶-۵۰']++;
                } else {
                    $ageGroups['۵۱+']++;
                }
            }

            // Get monthly registration data for the current year
            $monthlyData = [];
            $currentYear = date('Y');
            for ($month = 1; $month <= 12; $month++) {
                $count = PatientModel::whereYear('created_at', $currentYear)
                    ->whereMonth('created_at', $month)
                    ->count();

                $monthNames = [
                    1 => 'جنوري', 2 => 'فبروري', 3 => 'مارچ', 4 => 'اپریل',
                    5 => 'مۍ', 6 => 'جون', 7 => 'جولای', 8 => 'اګست',
                    9 => 'سپتمبر', 10 => 'اکتوبر', 11 => 'نومبر', 12 => 'دسمبر'
                ];

                $monthlyData[$monthNames[$month]] = $count;
            }

            // Get province distribution with error handling
            try {
                $provinceData = PatientModel::with('address')
                    ->get()
                    ->groupBy(function($patient) {
                        return $patient->address ? ($patient->address->P_Province ?? 'نامعلوم') : 'نامعلوم';
                    })
                    ->map(function($group) {
                        return $group->count();
                    })
                    ->toArray();
            } catch (\Exception $e) {
                \Log::error('Error getting province data: ' . $e->getMessage());
                $provinceData = ['نامعلوم' => 0];
            }

            return [
                'genderData' => $processedGenderData,
                'ageGroups' => $ageGroups,
                'monthlyData' => $monthlyData,
                'provinceData' => $provinceData
            ];

        } catch (\Exception $e) {
            \Log::error('Error getting patient chart data: ' . $e->getMessage());

            return [
                'genderData' => ['نارینه' => 0, 'ښځینه' => 0],
                'ageGroups' => ['۰-۱۸' => 0, '۱۹-۳۵' => 0, '۳۶-۵۰' => 0, '۵۱+' => 0],
                'monthlyData' => [],
                'provinceData' => []
            ];
        }
    }

    /**
     * Get dynamic chart data based on filters
     */
    public function getDynamicChartData(Request $request)
    {
        try {
            $type = $request->get('type', 'monthly');
            $period = $request->get('period', 'monthly');
            $filter = $request->get('filter', 'all');

            switch ($type) {
                case 'progress':
                    return response()->json($this->getProgressData($period));
                case 'demographics':
                    return response()->json($this->getDemographicsData($filter));
                case 'activities':
                    return response()->json($this->getRecentActivities());
                case 'questions':
                    return response()->json($this->getQuestionChartData());
                default:
                    return response()->json(['error' => 'Invalid type'], 400);
            }
        } catch (\Exception $e) {
            \Log::error('Error getting dynamic chart data: ' . $e->getMessage());
            return response()->json(['error' => 'Server error'], 500);
        }
    }

    /**
     * Get progress data based on period
     */
    private function getProgressData($period)
    {
        try {
            $data = [];
            $labels = [];

            switch ($period) {
                case 'weekly':
                    // Get last 7 days
                    for ($i = 6; $i >= 0; $i--) {
                        $date = now()->subDays($i);
                        $count = PatientModel::whereDate('created_at', $date)->count();
                        $labels[] = $date->format('D');
                        $data[] = $count;
                    }
                    break;

                case 'yearly':
                    // Get last 5 years
                    for ($i = 4; $i >= 0; $i--) {
                        $year = now()->subYears($i)->year;
                        $count = PatientModel::whereYear('created_at', $year)->count();
                        $labels[] = $year;
                        $data[] = $count;
                    }
                    break;

                case 'monthly':
                default:
                    // Get last 12 months
                    for ($i = 11; $i >= 0; $i--) {
                        $date = now()->subMonths($i);
                        $count = PatientModel::whereYear('created_at', $date->year)
                            ->whereMonth('created_at', $date->month)
                            ->count();

                        $monthNames = [
                            1 => 'جنوري', 2 => 'فبروري', 3 => 'مارچ', 4 => 'اپریل',
                            5 => 'مۍ', 6 => 'جون', 7 => 'جولای', 8 => 'اګست',
                            9 => 'سپتمبر', 10 => 'اکتوبر', 11 => 'نومبر', 12 => 'دسمبر'
                        ];

                        $labels[] = $monthNames[$date->month];
                        $data[] = $count;
                    }
                    break;
            }

            return [
                'labels' => $labels,
                'data' => $data,
                'period' => $period
            ];
        } catch (\Exception $e) {
            \Log::error('Error getting progress data: ' . $e->getMessage());
            return ['labels' => [], 'data' => [], 'period' => $period];
        }
    }

    /**
     * Get demographics data based on filter
     */
    private function getDemographicsData($filter)
    {
        try {
            switch ($filter) {
                case 'age':
                    $patients = PatientModel::all();
                    $ageGroups = [
                        '۰-۱۸' => 0,
                        '۱۹-۳۵' => 0,
                        '۳۶-۵۰' => 0,
                        '۵۱+' => 0
                    ];

                    foreach ($patients as $patient) {
                        $age = $patient->Patient_Age;
                        if ($age <= 18) {
                            $ageGroups['۰-۱۸']++;
                        } elseif ($age <= 35) {
                            $ageGroups['۱۹-۳۵']++;
                        } elseif ($age <= 50) {
                            $ageGroups['۳۶-۵۰']++;
                        } else {
                            $ageGroups['۵۱+']++;
                        }
                    }

                    return [
                        'labels' => array_keys($ageGroups),
                        'data' => array_values($ageGroups),
                        'filter' => $filter
                    ];

                case 'region':
                    $provinceData = PatientModel::with('address')
                        ->get()
                        ->groupBy(function($patient) {
                            return $patient->address->P_Province ?? 'نامعلوم';
                        })
                        ->map(function($group) {
                            return $group->count();
                        })
                        ->toArray();

                    return [
                        'labels' => array_keys($provinceData),
                        'data' => array_values($provinceData),
                        'filter' => $filter
                    ];

                case 'gender':
                default:
                    $genderData = PatientModel::selectRaw('Patient_Gender, COUNT(*) as count')
                        ->groupBy('Patient_Gender')
                        ->pluck('count', 'Patient_Gender')
                        ->toArray();

                    $genderLabels = [
                        'Male' => 'نارینه',
                        'Female' => 'ښځینه',
                        'male' => 'نارینه',
                        'female' => 'ښځینه',
                        'نارینه' => 'نارینه',
                        'ښځینه' => 'ښځینه'
                    ];

                    $processedGenderData = [];
                    foreach ($genderData as $gender => $count) {
                        $label = $genderLabels[$gender] ?? $gender;
                        $processedGenderData[$label] = $count;
                    }

                    return [
                        'labels' => array_keys($processedGenderData),
                        'data' => array_values($processedGenderData),
                        'filter' => $filter
                    ];
            }
        } catch (\Exception $e) {
            \Log::error('Error getting demographics data: ' . $e->getMessage());
            return ['labels' => [], 'data' => [], 'filter' => $filter];
        }
    }

    /**
     * Get recent activities
     */
    private function getRecentActivities()
    {
        try {
            $activities = [];

            // Recent patients (last 5)
            $recentPatients = PatientModel::latest()->take(5)->get();
            foreach ($recentPatients as $patient) {
                $patientName = $patient->Patient_Name ?? $patient->Patiet_Name ?? 'نامعلوم ناروغ';
                $activities[] = [
                    'type' => 'patient',
                    'icon' => 'user-plus',
                    'text' => 'نوی ناروغ ثبت شو: ' . $patientName,
                    'time' => $this->formatTimeAgo($patient->created_at),
                    'created_at' => $patient->created_at,
                    'priority' => 3
                ];
            }

            // Recent feedback (last 3)
            $recentFeedback = FeedbackModel::latest()->take(3)->get();
            foreach ($recentFeedback as $feedback) {
                $activities[] = [
                    'type' => 'feedback',
                    'icon' => 'comment',
                    'text' => 'نوی نظر راغی: ' . \Str::limit($feedback->feedback_text ?? 'نظر', 30),
                    'time' => $this->formatTimeAgo($feedback->created_at),
                    'created_at' => $feedback->created_at,
                    'priority' => 2
                ];
            }

            // Recent articles (last 3)
            $recentArticles = ArticalModel::latest()->take(3)->get();
            foreach ($recentArticles as $article) {
                $title = $article->title ?? $article->Article_Title ?? 'نوی مقاله';
                $activities[] = [
                    'type' => 'article',
                    'icon' => 'book-medical',
                    'text' => 'نوی مقاله اضافه شوه: ' . \Str::limit($title, 40),
                    'time' => $this->formatTimeAgo($article->created_at),
                    'created_at' => $article->created_at,
                    'priority' => 2
                ];
            }

            // Recent videos (last 2)
            $recentVideos = VideoModel::latest()->take(2)->get();
            foreach ($recentVideos as $video) {
                $title = $video->title ?? $video->Video_Title ?? 'نوی ویدیو';
                $activities[] = [
                    'type' => 'video',
                    'icon' => 'video',
                    'text' => 'نوی ویدیو اپلوډ شو: ' . \Str::limit($title, 40),
                    'time' => $this->formatTimeAgo($video->created_at),
                    'created_at' => $video->created_at,
                    'priority' => 2
                ];
            }

            // Recent news (last 2)
            $recentNews = NewsModel::latest()->take(2)->get();
            foreach ($recentNews as $news) {
                $title = $news->title ?? $news->News_Title ?? 'نوی خبر';
                $activities[] = [
                    'type' => 'news',
                    'icon' => 'newspaper',
                    'text' => 'نوی خبر اضافه شو: ' . \Str::limit($title, 40),
                    'time' => $this->formatTimeAgo($news->created_at),
                    'created_at' => $news->created_at,
                    'priority' => 1
                ];
            }

            // Recent questionnaire responses (last 3)
            $recentQuestions = QuestionerModel::with('patient')->latest()->take(3)->get();
            foreach ($recentQuestions as $question) {
                $patientName = $question->patient->Patient_Name ?? $question->patient->Patiet_Name ?? 'نامعلوم';
                $activities[] = [
                    'type' => 'questionnaire',
                    'icon' => 'poll',
                    'text' => 'نوی پوښتنلیک ډک شو د: ' . $patientName,
                    'time' => $this->formatTimeAgo($question->created_at),
                    'created_at' => $question->created_at,
                    'priority' => 3
                ];
            }

            // Sort by creation time and priority
            usort($activities, function($a, $b) {
                // First sort by time (most recent first)
                $timeComparison = $b['created_at'] <=> $a['created_at'];
                if ($timeComparison !== 0) {
                    return $timeComparison;
                }
                // If same time, sort by priority (higher priority first)
                return $b['priority'] <=> $a['priority'];
            });

            return array_slice($activities, 0, 6);
        } catch (\Exception $e) {
            \Log::error('Error getting recent activities: ' . $e->getMessage());
            return $this->getFallbackActivities();
        }
    }

    /**
     * Format time ago in Pashto
     */
    private function formatTimeAgo($datetime)
    {
        try {
            if (!$datetime) {
                return 'نامعلوم وخت';
            }

            $now = now();
            $diff = $now->diffInMinutes($datetime);

            if ($diff < 1) {
                return 'اوس مخکې';
            } elseif ($diff < 60) {
                return $diff . ' دقیقې وړاندې';
            } elseif ($diff < 1440) { // 24 hours
                $hours = floor($diff / 60);
                return $hours . ' ساعته وړاندې';
            } elseif ($diff < 10080) { // 7 days
                $days = floor($diff / 1440);
                return $days . ' ورځې وړاندې';
            } else {
                return $datetime->format('Y-m-d');
            }
        } catch (\Exception $e) {
            return 'نامعلوم وخت';
        }
    }

    /**
     * Get fallback activities when database fails
     */
    private function getFallbackActivities()
    {
        return [
            [
                'type' => 'system',
                'icon' => 'info-circle',
                'text' => 'سیسټم فعال دی',
                'time' => 'اوس مخکې',
                'created_at' => now(),
                'priority' => 1
            ],
            [
                'type' => 'system',
                'icon' => 'shield-alt',
                'text' => 'ډیټابیس تازه شو',
                'time' => '۱ ساعت وړاندې',
                'created_at' => now()->subHour(),
                'priority' => 1
            ]
        ];
    }

    /**
     * Get question chart data for dashboard - Dynamic from questioner_models
     */
    private function getQuestionChartData()
    {
        try {
            \Log::info('Getting dynamic question chart data from questioner_models');

            // Get all unique question numbers from questioner_models (excluding 11-15)
            $questionNumbers = QuestionerModel::select('Question_No')
                ->distinct()
                ->whereNotIn('Question_No', [11, 12, 13, 14, 15])
                ->orderBy('Question_No')
                ->pluck('Question_No')
                ->toArray();

            $questionData = [];

            if (count($questionNumbers) > 0) {
                foreach ($questionNumbers as $questionNumber) {
                    // Get a sample question record to get the question text and options
                    $sampleQuestion = QuestionerModel::where('Question_No', $questionNumber)
                        ->first();

                    if (!$sampleQuestion) {
                        continue;
                    }

                    // Count all patient responses for this question number
                    $responses = QuestionerModel::where('Question_No', $questionNumber)
                        ->whereNotNull('Selected_Option')
                        ->get();

                    // Initialize option counts
                    $optionCounts = [
                        'A' => 0,
                        'B' => 0,
                        'C' => 0,
                        'D' => 0
                    ];

                    // Count responses by Selected_Option (which contains A, B, C, D)
                    foreach ($responses as $response) {
                        $selectedOption = trim($response->Selected_Option);

                        // Handle both letter format (A, B, C, D) and text format
                        if (in_array($selectedOption, ['A', 'B', 'C', 'D'])) {
                            $optionCounts[$selectedOption]++;
                        } else {
                            // Map text responses back to letters
                            $textToLetter = [
                                'هیڅ نه' => 'A',
                                'لږه اندازه' => 'B',
                                'لږه ډېره اندازه' => 'C',
                                'ډېره اندازه' => 'D'
                            ];

                            if (isset($textToLetter[$selectedOption])) {
                                $optionCounts[$textToLetter[$selectedOption]]++;
                            }
                        }
                    }

                    // Get option labels from the sample question
                    $optionLabels = [
                        'A' => $sampleQuestion->A ?? 'هیڅ نه',
                        'B' => $sampleQuestion->B ?? 'لږه اندازه',
                        'C' => $sampleQuestion->C ?? 'لږه ډېره اندازه',
                        'D' => $sampleQuestion->D ?? 'ډېره اندازه'
                    ];

                    $totalResponses = array_sum($optionCounts);

                    $questionData[] = [
                        'question_number' => $questionNumber,
                        'question_text' => $sampleQuestion->Q_Discription ?? "پوښتنه $questionNumber",
                        'short_text' => 'پوښتنه ' . $questionNumber,
                        'options' => $optionLabels,
                        'counts' => $optionCounts,
                        'total_responses' => $totalResponses
                    ];

                    \Log::info("Question $questionNumber: Total responses = $totalResponses", $optionCounts);
                }
            } else {
                // Fallback to hardcoded questions if no questions in table
                $questions = [
                    "آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دې دا احساس کړی چي ښه یم؟",
                    "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي بدن مي د تقویت درمل ته اړتیا لري؟",
                    "آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دي د سستي او کمزوري احساس کړی؟",
                    "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې د ناروغۍ احساس کړی؟",
                    "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه د سر درد لري؟",
                    "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي سر دي په دستمال وتړم تر څو د درد کم شي؟",
                    "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي بدن مي ګرم یا یخ کیږي؟",
                    "آیا له دیرشو(۳۰) ورځو راهیسي د تشویش له امله بې خوبه شوی یې؟",
                    "آیا د خوب په منځ کې راویښیږې او خوب دې ګډوډیږي؟",
                    "آیا همېشه د فشار لاندې یې؟"
                ];

                $optionLabels = [
                    'A' => 'هیڅ نه',
                    'B' => 'لږه اندازه',
                    'C' => 'لږه ډېره اندازه',
                    'D' => 'ډېره اندازه'
                ];

                // Get statistics for each question (excluding 11-15)
                foreach ($questions as $index => $questionText) {
                    $questionNumber = $index + 1;

                    // Skip questions 11-15
                    if (in_array($questionNumber, [11, 12, 13, 14, 15])) {
                        continue;
                    }

                    // Get all responses for this question number
                    $responses = QuestionerModel::where('Question_No', $questionNumber)
                        ->whereNotNull('Selected_Option')
                        ->get();

                    $optionCounts = [
                        'A' => 0,
                        'B' => 0,
                        'C' => 0,
                        'D' => 0
                    ];

                    // Count responses for each option
                    foreach ($responses as $response) {
                        $selectedOption = $response->Selected_Option;
                        if (isset($optionCounts[$selectedOption])) {
                            $optionCounts[$selectedOption]++;
                        }
                    }

                    // Prepare data for this question
                    $questionData[] = [
                        'question_number' => $questionNumber,
                        'question_text' => $questionText,
                        'short_text' => 'پوښتنه ' . $questionNumber,
                        'options' => $optionLabels,
                        'counts' => $optionCounts,
                        'total_responses' => array_sum($optionCounts)
                    ];
                }
            }

            return $questionData;

        } catch (\Exception $e) {
            \Log::error('Error getting question chart data: ' . $e->getMessage());
            \Log::error('Stack trace: ' . $e->getTraceAsString());

            // Return empty array to prevent dashboard from breaking
            return [];
        }
    }

    /**
     * Get real-time question statistics for AJAX requests
     */
    public function getQuestionStatistics()
    {
        try {
            return response()->json([
                'success' => true,
                'data' => $this->getQuestionChartData(),
                'timestamp' => now()->toISOString()
            ]);
        } catch (\Exception $e) {
            \Log::error('Error getting question statistics: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'د پوښتنو ډیټا لوډولو کې ستونزه',
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Get question response summary
     */
    public function getQuestionResponseSummary()
    {
        try {
            $summary = [
                'total_questions' => QuestionerModel::distinct('Question_No')->count('Question_No'),
                'total_responses' => QuestionerModel::whereNotNull('Selected_Option')->count(),
                'total_patients' => QuestionerModel::distinct('Patient_Id')->count('Patient_Id'),
                'response_rate' => 0
            ];

            // Calculate response rate
            if ($summary['total_questions'] > 0 && $summary['total_patients'] > 0) {
                $expectedResponses = $summary['total_questions'] * $summary['total_patients'];
                $summary['response_rate'] = round(($summary['total_responses'] / $expectedResponses) * 100, 2);
            }

            return response()->json([
                'success' => true,
                'summary' => $summary,
                'timestamp' => now()->toISOString()
            ]);
        } catch (\Exception $e) {
            \Log::error('Error getting question response summary: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'د پوښتنو لنډیز لوډولو کې ستونزه'
            ], 500);
        }
    }
}








