<?php

namespace App\Http\Controllers;

use App\Models\ContactUsModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    /**
     * Store a newly created contact message in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'comment' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Create contact message
            $contact = new ContactUsModel();
            $contact->contact_name = $request->name;
            $contact->phone = $request->phone;
            $contact->comment = $request->comment;
            
            // If user is logged in, associate with user
            if (Auth::check()) {
                $contact->User_id = Auth::id(); // Use Auth::id() instead of Auth::user()->user_id
            } else {
                // For guest users, set User_id to null
                $contact->User_id = null;
            }
            
            $contact->save();

            return redirect()->back()->with('success', 'ستاسو پیغام په بریالیتوب سره ولیږل شو!');
        } catch (\Exception $e) {
            \Log::error('Error storing contact message: ' . $e->getMessage());
            
            return redirect()->back()
                ->with('error', 'تېروتنه: ' . $e->getMessage())
                ->withInput();
        }
    }
}
