<?php

use Laravel\Fortify\Features;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Schema;
use App\Http\Controllers\Maindashboard;
use App\Http\Controllers\BookController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\PaperController;
use App\Http\Controllers\VideoController;
use App\Http\Controllers\DoctorController;
use App\Http\Controllers\PolicyController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\AboutUsController;
use App\Http\Controllers\ArticleController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\PatientController;
use App\Http\Controllers\DatabaseController;
use App\Http\Controllers\FeedbackController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\QuestionsController;
use App\Http\Controllers\ContactUsController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\LanguagesController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\QuestionerController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\HealthSectionController;
use App\Http\Controllers\TypeofMentalHealthController;
use Laravel\Fortify\Http\Controllers\RegisteredUserController;
use Laravel\Fortify\Http\Controllers\AuthenticatedSessionController;

// Make sure Fortify routes are not being overridden
// Add these routes explicitly to ensure they work


// Show registration form for both guests and authenticated users
Route::get('/register', function () {
    return view('auth.register');
})->name('register');

// Let Fortify handle the registration POST request
// Route::post('/register', [App\Http\Controllers\Auth\RegisterController::class, 'register'])
//     ->name('register')
//     ->middleware('web');

// Dashboard route (protected)
Route::middleware('auth')->group(function () {
    // د پروژې اصلي دشبورډ ته راجع کول
    Route::get('/dashboard', [Maindashboard::class, 'DashboardIndex'])->name('dashboard');
});


Route::get('/login', function () {
    return view('auth.login');
})->middleware(['guest'])->name('login');

Route::get('/register', function () {
    return view('auth.register');
})->middleware(['guest'])->name('register');

// Original routes
Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
});

// the Main page route and it is releted routes
Route::get('/', function () {
    if (Auth::check()) {
        return redirect('/dashboard');
    }
    // Show the actual home page instead of redirecting to register
    return view('index');
})->name('home');

// Add a GET route for displaying the contact form
Route::get('/contact-us', function () {
    return view('index'); // Return the index view
})->name('contact-us.form');

// Keep the POST route for form submission
Route::post('/contact-us', [ContactController::class, 'store'])->name('Sroute');

// Search route
Route::get('/search', [App\Http\Controllers\SearchController::class, 'search'])->name('search');

// Update the ketabs route to accept search parameters
Route::get('/ketabs', [BookController::class, 'index'])->name('bookd.index');

Route::get('/policys',[BookController::class,'index'])->name('policys.index');

// Make sure you have this route defined
Route::get('/books', [BookController::class, 'index'])->name('books.index');
Route::get('/books/refresh', [BookController::class, 'refresh'])->name('books.refresh');
Route::get('/books/filter', [BookController::class, 'filter'])->name('books.filter');

// Patient filter routes
Route::get('/patient-filter', [PatientController::class, 'filter'])->name('patients.filter');

// the bellow is the Doctor page related routes

Route::resource('doctors', DoctorController::class);

// Doctor filter routes (using different pattern to avoid resource route conflicts)
Route::get('/doctor-filter', [DoctorController::class, 'filter'])->name('doctors.filter');

// Notification Management System Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/notification', [NotificationController::class, 'index'])->name('notifications.index');
    Route::post('/notifications/{id}/mark-as-read', [NotificationController::class, 'markAsRead'])->name('notifications.mark-as-read');
    Route::post('/notifications/mark-all-read', [NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-read');
    Route::get('/notifications/{notificationId}/patient/{patientId}', [NotificationController::class, 'viewPatient'])->name('notifications.view-patient');
    Route::delete('/notifications/{id}', [NotificationController::class, 'destroy'])->name('notifications.destroy');

    // API routes for notifications
    Route::get('/api/notifications', [NotificationController::class, 'getNotifications'])->name('api.notifications');
    Route::get('/api/notifications/count', [NotificationController::class, 'getUnreadCount'])->name('api.notifications.count');
});

// Test route removed - notification system is working

// Test notification routes removed

// Debug notification routes removed

// Doctor page route
Route::get('/doctor', [DoctorController::class, 'index'])->name('doctor.index');

// the bellow is the Book page routes

Route::get('/ketabs',[BookController::class,'Book']);

// the bellow is the About us Page Routes

Route::get('/aboutUs', [AboutUsController::class, 'AboutUs']);


// the bellow is the Quesioner page Routes
// د تشخیص صفحې روټ
Route::get('/question', [QuestionController::class, 'QuestionS'])->name('QuestionS');
Route::post('/question', [QuestionController::class, 'store'])->name('question.store');
// Route::post('/question',[QuestionController::class,'store'])->name('Pstore');

Route::post('/submit-mental-form', [TypeofMentalHealthController::class, 'submitForm']);

// the bellow is the News page Routes;

Route::get('/news',[NewsController::class,'News']);

// the Bellow is the Articles Page Routes

Route::get('/paper',[PaperController::class,'Papers']);


// the Bellow is the Types of mentalhealth Page Routes

Route::get('/typesofMental',[TypeofMentalHealthController::class,'TypeofMentlhealth']);

// the Bellow is the Health Sections Page Routes
Route::get('/healtsection',[HealthSectionController::class,'Healthsection']);

// the Bellow is the Policy Page Routes

Route::get('/policy',[PolicyController::class,'Policy']);

// د فیډبیک کنټرولر روټونه
Route::get('/feedback', [FeedbackController::class, 'index'])->name('feedback.index');
Route::post('/feedback', [FeedbackController::class, 'store'])->name('feedback.store');
Route::get('/feedback/{id}/edit', [FeedbackController::class, 'edit'])->name('feedback.edit');
Route::put('/feedback/{id}', [FeedbackController::class, 'update'])->name('feedback.update');
Route::delete('/feedback/{id}', [FeedbackController::class, 'destroy'])->name('feedback.destroy');

// Add this route for storing patient with questions
Route::post('/store-patient-with-questions', [App\Http\Controllers\QuestionController::class, 'storePatientWithQuestions'])->name('storePatientWithQuestions');

// Add this route
Route::get('/patient/{id}/result', [QuestionController::class, 'showResult'])->name('patient.result');

// Store patient with questions
Route::post('/store-patient-with-questions', [PatientController::class, 'storePatientWithQuestions'])->name('storePatientWithQuestions');

// Thank you page
Route::get('/thank-you', [App\Http\Controllers\QuestionController::class, 'thankYou'])->name('thank.you');






// Route::get('/languages', [LanguagesController::class, 'index']);

// the bellow routes is related to the dashboard of this project
Route::get('/dashboard',[Maindashboard::class,'DashboardIndex']);
// routes/web.php
Route::get('/dashboard', [Maindashboard::class, 'DashboardIndex'])
    ->name('dashboard')
    ->middleware('auth');
    // Protect with auth middleware
//  the bellow is the Book section of dashboard
Route::get('/bookd', [BookController::class, 'index'])->name('bookd.index');
Route::post('/bookd/store', [BookController::class, 'store'])->name('bookd.store');
Route::get('/book/{id}/edit', [BookController::class, 'edit'])->name('book.edit');
Route::put('/bookd/{id}', [BookController::class, 'update'])->name('bookd.update');
Route::delete('/book/{id}', [BookController::class, 'destroy'])->name('book.destroy');

// Book filter routes
Route::get('/books/refresh', [BookController::class, 'refresh'])->name('books.refresh');
Route::get('/books/filter', [BookController::class, 'filter'])->name('books.filter');

// Add these routes for papers
Route::get('/papers', [PaperController::class, 'Papers'])->name('papers.index');
Route::post('/papers/store', [PaperController::class, 'store'])->name('papers.store');
Route::get('/papers/{id}/edit', [PaperController::class, 'editp'])->name('papers.edit');
Route::put('/papers/{id}', [PaperController::class, 'updatep'])->name('papers.update');
Route::delete('/papers/{id}', [PaperController::class, 'destroyp'])->name('papers.destroy');

// Add policy routes
Route::get('/policy', [PolicyController::class, 'Policy'])->name('policy.index');
Route::post('/policy/store', [PolicyController::class, 'store'])->name('policy.store');
Route::get('/policy/{id}/edit', [PolicyController::class, 'edit'])->name('policy.edit');
Route::put('/policy/{id}', [PolicyController::class, 'update'])->name('policy.update');
Route::delete('/policy/{id}', [PolicyController::class, 'destroy'])->name('policy.destroy');

// Route::post('/articles', [BookController::class, 'store']);



//  the bellow is the Doctors section of dashboard
Route::get('/doctord', [Maindashboard::class, 'doctorcopy'])->name('admin.doctors.index');
Route::post('/doctors', [DoctorController::class, 'store'])->middleware('auth')->name('doctors.store');

// Doctor Routes - اصلي روټونه
Route::prefix('doctor')->name('doctor.')->group(function () {
    Route::get('/', [DoctorController::class, 'index'])->name('index');
    Route::post('/store', [DoctorController::class, 'store'])->name('store');
    Route::get('/edit/{id}', [DoctorController::class, 'edit'])->name('edit');
    Route::put('/update/{id}', [DoctorController::class, 'update'])->name('update');
    Route::delete('/destroy/{id}', [DoctorController::class, 'destroy'])->name('destroy');
    Route::get('/search', [DoctorController::class, 'search'])->name('search');
});

// Book/Ketabs search route
Route::get('/ketabs/search', [BookController::class, 'searchKetabs'])->name('ketabs.search');

// Policy search route
Route::get('/policy/search', [PolicyController::class, 'searchPolicy'])->name('policy.search');

// Paper search route
Route::get('/paper/search', [PaperController::class, 'searchPaper'])->name('paper.search');

// Admin dashboard doctor route
Route::get('/doctord', [DoctorController::class, 'index'])->name('doctord');

// اضافه کړئ: د doctors.index روټ
Route::get('/doctors', [DoctorController::class, 'index'])->name('doctors.index');





//  the bellow is the Feedback section of dashboard
Route::get('/feedbackd',[Maindashboard::class,'feedbackcopy']);
Route::get('/feedback', [FeedbackController::class, 'index'])->name('feedback.index');
Route::post('/feedback', [FeedbackController::class, 'store'])->name('feedback.store');
Route::get('/feedback/{id}/edit', [FeedbackController::class, 'edit'])->name('feedback.edit');
Route::put('/feedback/{id}', [FeedbackController::class, 'update'])->name('feedback.update');
Route::delete('/feedback/{id}', [FeedbackController::class, 'destroy'])->name('feedback.destroy');





//  the bellow is the News section of dashboard

Route::get('/newsd',[Maindashboard::class,'newscopy']);


Route::get('/newsd', [Maindashboard::class, 'newscopy'])->name('news.index');
Route::post('/news', [NewsController::class, 'store'])->name('news.store');
Route::get('/news/{id}/edit', [NewsController::class, 'edit'])->name('news.edit');
Route::put('/news/{id}', [NewsController::class, 'update'])->name('news.update');
Route::delete('/news/{id}', [NewsController::class, 'destroy'])->name('news.destroy');

// News routes - consolidated in one place
Route::prefix('news')->name('news.')->group(function () {
    Route::get('/', [App\Http\Controllers\Maindashboard::class, 'newscopy'])->name('index');
    Route::post('/', [App\Http\Controllers\NewsController::class, 'store'])->name('store');
    Route::get('/{id}/edit', [App\Http\Controllers\NewsController::class, 'edit'])->name('edit');
    Route::put('/{id}', [App\Http\Controllers\NewsController::class, 'update'])->name('update');
    Route::delete('/{id}', [App\Http\Controllers\NewsController::class, 'destroy'])->name('destroy');
});

// Remove any duplicate news routes to avoid conflicts
// Route::get('/newsd', [Maindashboard::class, 'newscopy'])->name('news.index'); // Remove this
// Route::post('/news', [NewsController::class, 'store'])->name('news.store'); // Remove this
// Route::get('/news/{id}/edit', [NewsController::class, 'edit'])->name('news.edit'); // Remove this
// Route::put('/news/{id}', [NewsController::class, 'update'])->name('news.update'); // Remove this
// Route::delete('/news/{id}', [NewsController::class, 'destroy'])->name('news.destroy'); // Remove this

// Redirect /newsd to /news for consistency
Route::redirect('/newsd', '/news');

//  the bellow is the Patient section of dashboard

Route::get('/patientd', [PatientController::class, 'index'])->name('patient.index');

//  the bellow is the questioner section of dashboard

Route::get('/questionerd',[Maindashboard::class,'questionercopy']);

//  the bellow is the Videos section of dashboard
// Route handled by VideoController below

// Add this route for language switching
Route::get('/language/{lang}', [App\Http\Controllers\LanguageController::class, 'translatePage'])->name('set.language');

// Debug route for translation testing
Route::get('/debug-translation', function () {
    return view('debug-translation');
});

// Patient routes
Route::prefix('patient')->name('patient.')->group(function () {
    Route::get('/', [PatientController::class, 'index'])->name('index');
    Route::post('/store', [PatientController::class, 'store'])->name('store');
    Route::get('/edit/{id}', [PatientController::class, 'edit'])->name('edit');
    Route::put('/update/{id}', [PatientController::class, 'update'])->name('update');
    Route::delete('/destroy/{id}', [PatientController::class, 'destroy'])->name('destroy');

    // Add the missing show route
    Route::get('/show/{id}', [PatientController::class, 'show'])->name('show');
});

Route::middleware('guest')->group(function () {
    Route::get('register', [App\Http\Controllers\Auth\RegisteredUserController::class, 'create'])
        ->name('register');
    Route::post('register', [App\Http\Controllers\Auth\RegisteredUserController::class, 'store']);
});

// Login route
Route::middleware('guest')->group(function () {
    Route::get('/login', function () {
        return view('auth.login');
    })->name('login');
});

// Duplicate home route removed - using the one above

// Logout Route
Route::post('/logout', [LoginController::class, 'logout'])->middleware('auth')->name('logout');

// Admin Routes
Route::middleware(['auth', 'admin'])->prefix('admin')->group(function () {
    Route::get('/dashboard', function () {
        return view('admin.dashboard');
    })->name('admin.dashboard');

    // Other admin routes...
});



// Make sure Fortify routes are not being overridden
// Add these routes explicitly to ensure they work


// Show registration form for both guests and authenticated users
Route::get('/register', function () {
    return view('auth.register');
})->name('register');

// Handle registration form submission
Route::post('/register', [RegisterController::class, 'store'])->name('register');

// Remove conflicting routes
Route::middleware('guest')->group(function () {
    // Remove this line if it exists
    // Route::get('register', [App\Http\Controllers\Auth\RegisteredUserController::class, 'create'])->name('register');

    // Remove this line if it exists
    // Route::post('register', [App\Http\Controllers\Auth\RegisteredUserController::class, 'store']);
});

// Book page route
Route::get('/books', [BookController::class, 'Book'])->name('books.index');

// If you have a route using ArticleController::Book, make sure it's correct
Route::get('/articles/books', [ArticleController::class, 'Book'])->name('articles.books');

// Add role column to users table
Route::get('/add-role-column', function () {
    try {
        if (!Schema::hasColumn('users', 'role')) {
            DB::statement('ALTER TABLE users ADD COLUMN role VARCHAR(255) DEFAULT "user" AFTER email_verified_at');
            return "Role column added successfully!";
        } else {
            return "Role column already exists!";
        }
    } catch (\Exception $e) {
        return "Error: " . $e->getMessage();
    }
});

// Check users table structure
Route::get('/check-users-table', function () {
    try {
        $columns = DB::select('SHOW COLUMNS FROM users');
        return response()->json($columns);
    } catch (\Exception $e) {
        return "Error: " . $e->getMessage();
    }
});

// Reset database view
Route::get('/admin/reset-database', function () {
    return view('admin.reset-database');
})->middleware(['auth', 'admin'])->name('admin.reset-database');

// Reset database action
Route::get('/reset-database', [DatabaseController::class, 'reset'])->middleware(['auth', 'admin'])->name('reset-database');

// Registration Routes
Route::get('register', [App\Http\Controllers\Auth\RegisterController::class, 'showRegistrationForm'])
    ->name('register');
Route::post('register', [App\Http\Controllers\Auth\RegisterController::class, 'register']);

// Dashboard routes
Route::get('/dashboard', [App\Http\Controllers\Maindashboard::class, 'DashboardIndex'])->name('dashboard');
Route::get('/main-dashboard', [App\Http\Controllers\Maindashboard::class, 'mainPageOfDashboard'])->name('main.dashboard');
Route::get('/dashboard/chart-data', [App\Http\Controllers\Maindashboard::class, 'getDynamicChartData'])->name('dashboard.chart.data');
Route::get('/dashboard/question-statistics', [App\Http\Controllers\Maindashboard::class, 'getQuestionStatistics'])->name('dashboard.question.statistics');
Route::get('/dashboard/question-summary', [App\Http\Controllers\Maindashboard::class, 'getQuestionResponseSummary'])->name('dashboard.question.summary');





// Doctor routes
Route::get('/doctors', [DoctorController::class, 'index'])->name('doctor.index');
Route::get('/doctor/create', [DoctorController::class, 'create'])->name('doctor.create');
Route::post('/doctor/store', [DoctorController::class, 'store'])->name('doctor.store');
Route::get('/doctor/{id}/edit', [DoctorController::class, 'edit'])->name('doctor.edit');
Route::put('/doctor/{id}', [DoctorController::class, 'update'])->name('doctor.update');
Route::delete('/doctor/{id}', [DoctorController::class, 'destroy'])->name('doctor.destroy');

// Patient routes
Route::get('/patients', [App\Http\Controllers\Maindashboard::class, 'patientcopy'])->name('patients');

Auth::routes();

Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

// Feedback routes
Route::get('/feedback', [App\Http\Controllers\FeedbackController::class, 'index'])->name('feedback.index');
Route::post('/feedback', [App\Http\Controllers\FeedbackController::class, 'store'])->name('feedback.store');
Route::get('/feedback/{id}/edit', [App\Http\Controllers\FeedbackController::class, 'edit'])->name('feedback.edit');
Route::put('/feedback/{id}', [App\Http\Controllers\FeedbackController::class, 'update'])->name('feedback.update');
Route::delete('/feedback/{id}', [App\Http\Controllers\FeedbackController::class, 'destroy'])->name('feedback.destroy');

// Legacy questioner routes (keeping for backward compatibility)
Route::get('/questioner/seed', [App\Http\Controllers\QuestionController::class, 'runQuestionsSeeder'])->name('questioner.seed');

// Route to display questionnaire with questions from database
Route::get('/questionnaire', [App\Http\Controllers\QuestionController::class, 'showQuestionnaire'])->name('questionnaire');

// Direct SQL route to get all questions
Route::get('/questioner-direct', [App\Http\Controllers\QuestionController::class, 'getAllQuestions'])->name('questioner.direct');

// د عامو کارونکو لپاره روټونه (بې له میډلویر)
Route::get('/newscopy', function () {
    return view('dashboardofproject.newscopy');
})->name('newscopy');

Route::get('/videocopy', function () {
    return view('dashboardofproject.videocopy');
})->name('videocopy');

// د اډمین پینل لپاره روټونه (د میډلویر سره)
Route::middleware(['auth'])->group(function () {
    // نور روټونه دلته دي...
});

// د پوښتنو سیډر اجرا کولو او لرې کولو روټونه
Route::get('/run-questions-seeder', [QuestionController::class, 'runQuestionsSeeder'])->name('questions.run-seeder');
Route::get('/delete-all-questions', [QuestionController::class, 'deleteAllQuestions'])->name('questions.delete-all');

// Email testing route
Route::get('/test-email', function() {
    return view('test-email');
})->name('test.email');

// Gmail setup guide
Route::get('/gmail-setup', function() {
    return view('gmail-setup');
})->name('gmail.setup');

Route::post('/send-test-email', function(\Illuminate\Http\Request $request) {
    try {
        $email = $request->email;

        \Illuminate\Support\Facades\Mail::raw('This is a test email from KU Mental Health System. If you receive this, your email configuration is working correctly!', function ($message) use ($email) {
            $message->to($email)
                    ->subject('Test Email - KU Mental Health System');
        });

        return back()->with('success', 'Test email sent successfully to: ' . $email);
    } catch (\Exception $e) {
        \Log::error('Test email failed: ' . $e->getMessage());
        return back()->with('error', 'Email failed: ' . $e->getMessage());
    }
})->name('send.test.email');

// Notification routes removed





// Dashboard doctor route
Route::get('/doctorcopy', [App\Http\Controllers\Maindashboard::class, 'doctorcopy'])->name('doctorcopy');
// Fix the doctorcopy route to use the correct name
Route::get('/doctorcopy', [App\Http\Controllers\DoctorController::class, 'index'])->name('doctord.index');

// Add this as a backup route with the same name
Route::get('/doctord', [App\Http\Controllers\DoctorController::class, 'index'])->name('doctord.index');
// Remove any conflicting routes first (if they exist)
// Route::get('/doctor', [DoctorController::class, 'index'])->name('doctor.index');

// Make sure this route is AFTER any route groups that might override it
Route::get('/doctorcopy', [App\Http\Controllers\Maindashboard::class, 'doctorcopy'])->name('doctorcopy');





// Questions CRUD routes (for the questions table)
Route::resource('questions', QuestionsController::class);
Route::get('questions/{id}/edit', [QuestionsController::class, 'edit'])->name('questions.edit');
Route::get('/questioner', [QuestionsController::class, 'index'])->name('questioner.index');
Route::post('/questioner', [QuestionsController::class, 'store'])->name('questioner.store');
Route::get('/questioner/{id}/edit', [QuestionsController::class, 'edit'])->name('questioner.edit');
Route::put('/questioner/{id}', [QuestionsController::class, 'update'])->name('questioner.update');
Route::delete('/questioner/{id}', [QuestionsController::class, 'destroy'])->name('questioner.destroy');
Route::get('/questions-for-questionnaire', [QuestionsController::class, 'getQuestionsForQuestionnaire'])->name('questions.for.questionnaire');

// Questioner routes (for the questioner_models table - patient responses)
Route::get('patients/{patient}/questions', [QuestionerController::class, 'patientQuestions'])->name('patients.questions');
Route::get('patients/{patient}/answer', [QuestionerController::class, 'answerForm'])->name('patients.answer');
Route::post('patients/{patient}/save-answers', [QuestionerController::class, 'saveAnswers'])->name('patients.save-answers');

// Paper routes for dashboard
Route::get('/papers', [PaperController::class, 'index'])->name('papers.index');
Route::post('/papers/store', [PaperController::class, 'store'])->name('papers.store');
Route::get('/papers/edit/{id}', [PaperController::class, 'edit'])->name('papers.edit');
Route::put('/papers/update/{id}', [PaperController::class, 'update'])->name('papers.update');
Route::delete('/papers/destroy/{id}', [PaperController::class, 'destroy'])->name('papers.destroy');

// Paper routes for public view
Route::get('/paper', [PaperController::class, 'Papers'])->name('paper.public');

// Book routes
Route::get('/bookcopy', [App\Http\Controllers\Maindashboard::class, 'bookcopy'])->name('bookcopy');
Route::get('/bookd', [App\Http\Controllers\BookController::class, 'index'])->name('bookd.index');
Route::get('/books/filter', [App\Http\Controllers\BookController::class, 'filter'])->name('books.filter');
Route::get('/bookd/create', [App\Http\Controllers\BookController::class, 'create'])->name('bookd.create');
Route::post('/bookd/store', [App\Http\Controllers\BookController::class, 'store'])->name('bookd.store');
Route::get('/bookd/{id}/edit', [App\Http\Controllers\BookController::class, 'edit'])->name('bookd.edit');
Route::put('/bookd/{id}', [App\Http\Controllers\BookController::class, 'update'])->name('bookd.update');
Route::delete('/bookd/{id}', [App\Http\Controllers\BookController::class, 'destroy'])->name('bookd.destroy');

// Doctor routes for dashboard
Route::get('/doctorcopy', [App\Http\Controllers\DoctorController::class, 'index'])->name('doctord.index');
Route::get('/doctord', [App\Http\Controllers\DoctorController::class, 'index'])->name('doctord.index');
Route::post('/doctor/store', [App\Http\Controllers\DoctorController::class, 'store'])->name('doctor.store');
Route::put('/doctor/update/{id}', [App\Http\Controllers\DoctorController::class, 'update'])->name('doctor.update');
Route::delete('/doctor/{id}', [App\Http\Controllers\DoctorController::class, 'destroy'])->name('doctor.destroy');

// Remove any conflicting routes
// Route::get('/doctorcopy', [App\Http\Controllers\Maindashboard::class, 'doctorcopy'])->name('doctorcopy');

Route::get('/patient/report', [App\Http\Controllers\PatientController::class, 'report'])->name('patient.report');
Route::get('/patient/report/export', [PatientController::class, 'exportReport'])->name('patient.report.export');

// News routes for public site
Route::get('/news', [NewsController::class, 'News'])->name('news.public');

// News routes for dashboard (admin)
Route::middleware('auth')->group(function () {
    Route::get('/newsd', [NewsController::class, 'index'])->name('news.index');
    Route::post('/newsd/store', [NewsController::class, 'store'])->name('news.store');
    Route::get('/newsd/{id}/edit', [NewsController::class, 'edit'])->name('news.edit');
    Route::put('/newsd/{id}', [NewsController::class, 'update'])->name('news.update');
    Route::delete('/newsd/{id}', [NewsController::class, 'destroy'])->name('news.destroy');
});

// Video routes for dashboard
Route::middleware(['auth'])->group(function () {
    // Change the resource route to explicit routes to avoid conflicts
    Route::get('/videod', [VideoController::class, 'index'])->name('videos.index');
    Route::get('/videod/create', [VideoController::class, 'create'])->name('videos.create');
    Route::post('/videod', [VideoController::class, 'store'])->name('videos.store');
    Route::get('/videod/{id}/edit', [VideoController::class, 'edit'])->name('videos.edit');
    Route::put('/videod/{id}', [VideoController::class, 'update'])->name('videos.update');
    Route::delete('/videod/{id}', [VideoController::class, 'destroy'])->name('videos.destroy');
    Route::get('/videod/{id}', [VideoController::class, 'show'])->name('videos.show');
});

// Public video routes
Route::get('/videos', [VideoController::class, 'showVideos'])->name('videos.public');
Route::get('/videos/{id}', [VideoController::class, 'showVideo'])->name('videos.show.public');

// Video routes for frontend
Route::get('/videos', [VideoController::class, 'showVideos'])->name('videos.public');

// CSRF Token refresh route
Route::get('/csrf-token-refresh', function () {
    return response()->json([
        'token' => csrf_token()
    ]);
});

// Diagnostic route to check questions table
Route::get('/check-questions-table', function() {
    try {
        // Check if the table exists
        if (!Schema::hasTable('questioner_models')) {
            return "Table 'questioner_models' does not exist!";
        }
        
        // Get the column names
        $columns = Schema::getColumnListing('questioner_models');
        
        // Count the records
        $count = DB::table('questioner_models')->count();
        
        // Get a sample record
        $sample = DB::table('questioner_models')->first();
        
        return [
            'table_exists' => true,
            'columns' => $columns,
            'record_count' => $count,
            'sample_record' => $sample
        ];
    } catch (\Exception $e) {
        return "Error: " . $e->getMessage();
    }
});

// Question management routes
Route::get('/questions', [App\Http\Controllers\QuestionController::class, 'index'])->name('questions.index');
Route::post('/questions', [App\Http\Controllers\QuestionController::class, 'store'])->name('questions.store');
Route::get('/questions/{id}/edit', [App\Http\Controllers\QuestionController::class, 'edit'])->name('questions.edit');
Route::put('/questions/{id}', [App\Http\Controllers\QuestionController::class, 'update'])->name('questions.update');
Route::delete('/questions/{id}', [App\Http\Controllers\QuestionController::class, 'destroy'])->name('questions.destroy');

// Question bank (for patients)
Route::get('/question-bank', [App\Http\Controllers\QuestionController::class, 'index'])->name('question.bank');
Route::post('/store-patient-with-questions', [App\Http\Controllers\QuestionController::class, 'storePatientWithQuestions'])->name('storePatientWithQuestions');

// Test route for saving a question
Route::get('/test-save-question', [QuestionController::class, 'testSaveQuestion'])->name('test.save.question');





Route::get('/debug-questions', function() {
    $questions = \App\Models\Question::all();
    return response()->json([
        'questions_table_count' => $questions->count(),
        'questions' => $questions,
        'questioner_models_count' => \App\Models\QuestionerModel::whereNull('Patient_Id')->count()
    ]);
});

// Test route removed



Route::post('/store-patient-with-questions', [PatientController::class, 'storePatientWithQuestions'])->name('storePatientWithQuestions');

// د ناروغانو روټونه
Route::resource('patient', PatientController::class);

// د ناروغانو تازه کولو لپاره ځانګړی روټ
Route::post('/patient/{id}', [App\Http\Controllers\PatientController::class, 'update'])->name('patient.update');
Route::put('/patient/{id}', [App\Http\Controllers\PatientController::class, 'update']);

// د ناروغ او د هغه د ځوابونو ذخیره کولو لپاره روټ
Route::post('/store-patient-with-questions', [PatientController::class, 'storePatientWithQuestions'])->name('storePatientWithQuestions');
















































