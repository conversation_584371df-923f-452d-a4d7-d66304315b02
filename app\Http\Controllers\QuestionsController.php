<?php

namespace App\Http\Controllers;

use App\Models\Question;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\QuestionerModel;

class QuestionsController extends Controller
{
    /**
     * Display a listing of the questions with search and pagination.
     */
    public function index(Request $request)
    {
        $query = Question::query();

        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function($q) use ($searchTerm) {
                $q->where('question_text', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('question_type', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('A', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('B', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('C', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('D', 'LIKE', "%{$searchTerm}%");
            });
        }

        // Filter by question type
        if ($request->has('question_type') && !empty($request->question_type)) {
            $query->where('question_type', $request->question_type);
        }

        // Order by latest first
        $query->orderBy('created_at', 'desc');

        // Paginate results
        $questions = $query->paginate(10)->appends($request->query());

        return view('dashboardofproject.questioner', compact('questions'));
    }

    /**
     * Show the form for creating a new question.
     */
    public function create()
    {
        return view('dashboardofproject.questioner');
    }

    /**
     * Display the specified question.
     */
    public function show($id)
    {
        $question = Question::findOrFail($id);
        return response()->json($question);
    }

    /**
     * Show the form for editing the specified question.
     */
    public function edit($id)
    {
        $question = Question::findOrFail($id);
        return response()->json($question);
    }

    /**
     * Store a newly created question in storage.
     */
    public function store(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'question_text' => 'required|string|max:500',
            'question_type' => 'required|string',
            'A' => 'required|string|max:255',
            'B' => 'required|string|max:255',
            'C' => 'required|string|max:255',
            'D' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput()
                ->with('error', 'د پوښتنې په ثبتولو کې ستونزه: لطفۍ ټول فیلډونه په سم ډول ډک کړئ.');
        }

        try {
            // Create new question
            $question = Question::create([
                'question_text' => $request->question_text,
                'question_type' => $request->question_type,
                'A' => $request->A,
                'B' => $request->B,
                'C' => $request->C,
                'D' => $request->D,
            ]);

            return redirect()->route('questioner.index')
                ->with('success', 'پوښتنه په بریالیتوب سره ثبت شوه!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'د پوښتنې په ثبتولو کې ستونزه: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Update the specified question in storage.
     */
    public function update(Request $request, $id)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'question_text' => 'required|string|max:500',
            'question_type' => 'required|string|in:mental,physical,social',
            'A' => 'required|string|max:255',
            'B' => 'required|string|max:255',
            'C' => 'required|string|max:255',
            'D' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput()
                ->with('error', 'د پوښتنې په تازه کولو کې ستونزه: لطفۍ ټول فیلډونه په سم ډول ډک کړئ.');
        }

        try {
            // Find the question
            $question = Question::findOrFail($id);
            
            // Update the question
            $question->update([
                'question_text' => $request->question_text,
                'question_type' => $request->question_type,
                'A' => $request->A,
                'B' => $request->B,
                'C' => $request->C,
                'D' => $request->D,
            ]);

            return redirect()->route('questioner.index')
                ->with('success', 'پوښتنه په بریالیتوب سره تازه شوه!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'د پوښتنې په تازه کولو کې ستونزه: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified question from storage.
     */
    public function destroy($id)
    {
        try {
            // Find the question
            $question = Question::findOrFail($id);
            
            // Delete the question
            $question->delete();

            return redirect()->route('questioner.index')
                ->with('success', 'پوښتنه په بریالیتوب سره لرې شوه!');
        } catch (\Exception $e) {
            return redirect()->route('questioner.index')
                ->with('error', 'د پوښتنې په لرې کولو کې ستونزه: ' . $e->getMessage());
        }
    }

    /**
     * Get questions for the questionnaire dynamically
     */
    public function getQuestionsForQuestionnaire()
    {
        try {
            $questions = Question::orderBy('created_at', 'asc')->get();
            return response()->json([
                'success' => true,
                'questions' => $questions
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'د پوښتنو په راوړلو کې ستونزه: ' . $e->getMessage()
            ]);
        }
    }
}