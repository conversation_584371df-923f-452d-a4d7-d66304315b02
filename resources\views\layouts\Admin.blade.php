<!DOCTYPE html>
<html lang="ps" dir="rtl">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'د کندهار پوهنتون - روانی روغتیا مرکز')</title>

    <!-- Fonts and Icons -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700;800&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">

    <!-- Custom Styles -->
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

            --sidebar-width: 280px;
            --header-height: 70px;
            --border-radius: 12px;
            --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.08);
            --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.12);
            --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.16);

            --text-primary: #2d3748;
            --text-secondary: #718096;
            --text-muted: #a0aec0;
            --bg-primary: #ffffff;
            --bg-secondary: #f7fafc;
            --bg-tertiary: #edf2f7;

            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Arabic', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            direction: rtl;
            color: var(--text-primary);
            overflow-x: hidden;
        }

        /* Professional Header Styles */
        header {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%);
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(229, 231, 235, 0.8);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        /* Professional Action Buttons */
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .action-btn:hover .absolute {
            transform: translateX(100%);
            opacity: 0.2;
        }

        .action-btn:active {
            transform: translateY(0);
        }

        /* Notifications button styles removed */

        .new-user-btn:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }

        .logout-btn:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        }

        /* Professional Search Styles */
        .search-wrapper:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: #d1d5db;
        }

        .search-wrapper:focus-within {
            border-color: #6366f1;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
        }

        .search-input:focus {
            outline: none;
        }

        .search-input::placeholder {
            color: #9ca3af;
        }

        .search-btn:hover {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
        }

        .search-btn:hover .absolute {
            transform: translateX(100%);
            opacity: 0.2;
        }

        .search-btn:active {
            transform: scale(0.98);
        }

        /* Professional Search Results */
        .search-results {
            animation: fadeInDown 0.3s ease-out;
            max-height: 300px;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .search-result-item {
            padding: 12px 16px;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .search-result-item:hover {
            background: #f8fafc;
            transform: translateX(2px);
        }

        .search-result-item:last-child {
            border-bottom: none;
        }

        /* Responsive Header */
        @media (max-width: 1024px) {
            .search-input {
                width: 250px !important;
            }

            .search-btn span {
                display: none;
            }

            .action-btn span {
                display: none;
            }
        }

        /* Enhanced Scrollbar Styles */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(241, 245, 249, 0.5);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: scale(1.1);
        }

        /* Sidebar specific scrollbar - Enhanced visibility */
        #sidebar nav::-webkit-scrollbar {
            width: 8px;
        }

        #sidebar nav::-webkit-scrollbar-track {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 4px;
            margin: 4px 0;
        }

        #sidebar nav::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.5) 0%, rgba(118, 75, 162, 0.5) 100%);
            border-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        #sidebar nav::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
            transform: scale(1.1);
        }

        /* Force scrollbar to always show for testing */
        #sidebar nav {
            overflow-y: scroll !important;
        }

        /* Enhanced sidebar navigation styles with light blue active state */
        .nav-item {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            margin-bottom: 8px;
            border-radius: 16px;
            overflow: hidden;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .nav-item:hover::before {
            opacity: 1;
        }

        /* Blue active state for sidebar buttons */
        .nav-item.active,
        .nav-item:active,
        .nav-item.clicked {
            background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%) !important;
            color: white !important;
            transform: translateX(8px) scale(1.02);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
            border-left: 4px solid rgba(255, 255, 255, 0.3);
        }

        .nav-item.active::before {
            opacity: 0;
        }

        .nav-item.active i {
            color: white !important;
        }

        .nav-item.active .bg-blue-100,
        .nav-item.active .bg-green-100,
        .nav-item.active .bg-purple-100,
        .nav-item.active .bg-orange-100,
        .nav-item.active .bg-indigo-100,
        .nav-item.active .bg-teal-100,
        .nav-item.active .bg-yellow-100,
        .nav-item.active .bg-red-100 {
            background: rgba(255, 255, 255, 0.25) !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .nav-item:hover:not(.active) {
            transform: translateX(6px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 197, 253, 0.05) 100%);
        }

        /* Enhanced icon containers */
        .nav-item .w-12 {
            transition: all 0.3s ease;
        }

        .nav-item:hover .w-12 {
            transform: scale(1.1);
        }

        .nav-item.active .w-12 {
            transform: scale(1.15);
        }

        /* Smooth text transitions */
        .nav-item span {
            transition: all 0.3s ease;
        }

        /* Active indicator dot animation */
        .nav-item .w-2 {
            transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        /* Chevron animation */
        .nav-item .fa-chevron-left {
            transition: all 0.3s ease;
        }

        .nav-item:hover .fa-chevron-left {
            transform: translateX(-3px);
        }

        .nav-item.active .fa-chevron-left {
            transform: translateX(-5px) scale(1.2);
        }

        /* Ripple effect on click */
        .nav-item {
            position: relative;
            overflow: hidden;
        }

        .nav-item::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(59, 130, 246, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .nav-item:active::after {
            width: 300px;
            height: 300px;
        }
    </style>

    <!-- Tailwind CSS - Development version -->
    <script>
        // Suppress console warnings for development
        const originalWarn = console.warn;
        console.warn = function(...args) {
            if (args[0] && args[0].includes('cdn.tailwindcss.com should not be used in production')) {
                return; // Suppress this specific warning
            }
            originalWarn.apply(console, args);
        };
    </script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
    tailwind.config = {
        theme: {
            extend: {
                colors: {
                    primary: '#667eea',
                    secondary: '#764ba2',
                    accent: '#f093fb',
                    success: '#4facfe',
                    warning: '#43e97b',
                    danger: '#fa709a'
                },
                fontFamily: {
                    'arabic': ['Noto Sans Arabic', 'sans-serif'],
                    'inter': ['Inter', 'sans-serif']
                }
            }
        }
    }
    </script>
    <script>
        // Restore original console.warn after Tailwind loads
        setTimeout(() => {
            console.warn = originalWarn;
        }, 1000);
    </script>

    <!-- Chart.js Library - Only load if needed -->
    @stack('chart-scripts')

    <script>
        // Global Chart.js loader function
        window.loadChartJS = function(callback) {
            if (typeof Chart !== 'undefined') {
                if (callback) callback();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js';
            script.onload = function() {
                console.log('Chart.js loaded successfully');

                // Load adapter after Chart.js
                const adapterScript = document.createElement('script');
                adapterScript.src = 'https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js';
                adapterScript.onload = function() {
                    console.log('Chart.js adapter loaded successfully');
                    if (callback) callback();
                };
                adapterScript.onerror = function() {
                    console.warn('Chart.js adapter failed to load');
                    if (callback) callback();
                };
                document.head.appendChild(adapterScript);
            };
            script.onerror = function() {
                console.error('Chart.js failed to load');
                if (callback) callback();
            };
            document.head.appendChild(script);
        };
    </script>

    <!-- Additional Styles Stack -->
    @stack('styles')
</head>

<body>
    <!-- Loading Screen Removed -->

    <!-- Mobile Header -->
    <header class="lg:hidden fixed top-0 left-0 right-0 z-40 bg-white shadow-lg border-b border-gray-200">
        <div class="flex items-center justify-between px-4 py-3">
            <div class="flex items-center space-x-3 space-x-reverse">
                <img src="{{url('public/../imagese/logo.PNG')}}" alt="لوګو" class="w-10 h-10 rounded-full">
                <div>
                    <h1 class="text-lg font-bold text-gray-800">د کندهار پوهنتون</h1>
                    <p class="text-sm text-gray-600">روانی روغتیا مرکز</p>
                </div>
            </div>
            <button id="mobileMenuBtn" class="p-2 rounded-lg bg-gradient-to-r from-primary to-secondary text-white shadow-lg hover:shadow-xl transition-all duration-300">
                <i class="fas fa-bars text-lg"></i>
            </button>
        </div>
    </header>

    <!-- Overlay for mobile menu -->
    <div id="overlay" class="fixed inset-0 bg-black bg-opacity-50 z-30 hidden lg:hidden backdrop-blur-sm"></div>

    <!-- Sidebar -->
    <aside id="sidebar" class="fixed top-0 right-0 h-full w-80 bg-white shadow-2xl z-40 transform translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out overflow-hidden flex flex-col">
        <!-- Close button for mobile -->
        <button id="closeMenu" class="absolute top-4 left-4 lg:hidden p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-200">
            <i class="fas fa-times text-gray-600"></i>
        </button>

        <!-- Sidebar Header -->
        <div class="p-6 border-b border-gray-100">
            <a href="{{ url('/dashboard') }}" class="flex items-center space-x-4 space-x-reverse group">
                <div class="relative">
                    <img src="{{url('public/../imagese/logo.PNG')}}" alt="لوګو" class="w-16 h-16 rounded-full shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                    <!-- <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white"></div> -->
                </div>
                <div class="flex-1">
                    <h2 class="text-xl font-bold text-gray-800 group-hover:text-primary transition-colors duration-200">د کندهار پوهنتون</h2>
                    <p class="text-sm text-gray-600 mt-1">روانی روغتیا مرکز</p>
                   
                </div>
            </a>
        </div>

        <!-- Navigation Menu -->
        <nav class="flex-1 px-4 pb-6 overflow-y-auto min-h-0 relative" style="scrollbar-width: thin; scrollbar-color: #667eea #f1f1f1;">
            <!-- Scroll indicator gradient -->
            <div class="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent pointer-events-none z-10"></div>
            <div class="space-y-2">
                <!-- Dashboard -->
                <a href="{{url('/dashboard')}}" class="nav-item group flex items-center px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-primary hover:to-secondary hover:text-white transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-blue-100 group-hover:bg-white group-hover:bg-opacity-20 transition-all duration-300">
                        <i class="fas fa-home text-blue-600 group-hover:text-white"></i>
                    </div>
                    <span class="mr-3 font-medium">کنټرول پاڼه</span>
                    <i class="fas fa-chevron-left mr-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                </a>

                <!-- Books -->
                <a href="{{url('/bookd')}}" class="nav-item group flex items-center px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-green-500 hover:to-teal-500 hover:text-white transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-green-100 group-hover:bg-white group-hover:bg-opacity-20 transition-all duration-300">
                        <i class="fas fa-book text-green-600 group-hover:text-white"></i>
                    </div>
                    <span class="mr-3 font-medium">کتابونه مقالي پالیسی</span>
                    <i class="fas fa-chevron-left mr-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                </a>

                <!-- Doctors -->
                <a href="{{url('/doctorcopy')}}" class="nav-item group flex items-center px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-purple-500 hover:to-pink-500 hover:text-white transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-purple-100 group-hover:bg-white group-hover:bg-opacity-20 transition-all duration-300">
                        <i class="fas fa-user-md text-purple-600 group-hover:text-white"></i>
                    </div>
                    <span class="mr-3 font-medium">ډاکټران</span>
                    <i class="fas fa-chevron-left mr-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                </a>

                <!-- Patients -->
                <a href="{{url('/patientd')}}" class="nav-item group flex items-center px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-orange-500 hover:to-red-500 hover:text-white transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-orange-100 group-hover:bg-white group-hover:bg-opacity-20 transition-all duration-300">
                        <i class="fas fa-users text-orange-600 group-hover:text-white"></i>
                    </div>
                    <span class="mr-3 font-medium">ناروغان</span>
                    <i class="fas fa-chevron-left mr-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                </a>

                <!-- Questionnaire -->
                <a href="{{url('/questioner')}}" class="nav-item group flex items-center px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-indigo-500 hover:to-blue-500 hover:text-white transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-indigo-100 group-hover:bg-white group-hover:bg-opacity-20 transition-all duration-300">
                        <i class="fas fa-poll text-indigo-600 group-hover:text-white"></i>
                    </div>
                    <span class="mr-3 font-medium">پوښتنلیک</span>
                    <i class="fas fa-chevron-left mr-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                </a>

                <!-- Feedback -->
                <a href="{{url('/feedbackd')}}" class="nav-item group flex items-center px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-teal-500 hover:to-cyan-500 hover:text-white transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-teal-100 group-hover:bg-white group-hover:bg-opacity-20 transition-all duration-300">
                        <i class="fas fa-comments text-teal-600 group-hover:text-white"></i>
                    </div>
                    <span class="mr-3 font-medium">نظریات</span>
                    <i class="fas fa-chevron-left mr-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                </a>

                <!-- Notifications -->
                <a href="{{ route('notifications.index') }}" class="nav-item group flex items-center px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-blue-500 hover:to-indigo-500 hover:text-white transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-blue-100 group-hover:bg-white group-hover:bg-opacity-20 transition-all duration-300">
                        <i class="fas fa-bell text-blue-600 group-hover:text-white"></i>
                    </div>
                    <span class="mr-3 font-medium">خبرتیاوې</span>
                    <span id="notification-badge" class="bg-red-500 text-white text-xs rounded-full px-2 py-1 ml-2 hidden">0</span>
                    <i class="fas fa-chevron-left mr-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                </a>

                <!-- News -->
                <a href="{{ url('/newsd') }}" class="nav-item group flex items-center px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-yellow-500 hover:to-orange-500 hover:text-white transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-yellow-100 group-hover:bg-white group-hover:bg-opacity-20 transition-all duration-300">
                        <i class="fas fa-newspaper text-yellow-600 group-hover:text-white"></i>
                    </div>
                    <span class="mr-3 font-medium">{{ translateText('خبرونه') }}</span>
                    <i class="fas fa-chevron-left mr-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                </a>

                <!-- Videos -->
                <!-- <a href="{{url('/videod')}}" class="nav-item group flex items-center px-4 py-3 rounded-xl text-gray-700 hover:bg-gradient-to-r hover:from-red-500 hover:to-pink-500 hover:text-white transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-red-100 group-hover:bg-white group-hover:bg-opacity-20 transition-all duration-300">
                        <i class="fas fa-video text-red-600 group-hover:text-white"></i>
                    </div>
                    <span class="mr-3 font-medium">ویدیوګانی</span>
                    <i class="fas fa-chevron-left mr-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                </a> -->

            

          
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="lg:mr-80 min-h-screen pt-16 lg:pt-0">
        <!-- Top Header Bar -->
        <header class="hidden lg:flex items-center justify-between bg-gradient-to-r from-white to-gray-50 shadow-lg border-b border-gray-200 px-8 py-4 sticky top-0 z-20" style="backdrop-filter: blur(10px); border-bottom: 1px solid rgba(229, 231, 235, 0.8);">

            <!-- Left Side: Professional Search Section -->
            <div class="search-section">
                <div class="search-container relative">
                    <h1 style=" font-size :1.7em; ">کندهار پوهنتون د انسجام آمریت اړوند روغتیا مرکز</h1>
                    <!-- <div class="search-wrapper flex items-center" style="background: white; border-radius: 10px; border: 1px solid #e5e7eb; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06); transition: all 0.3s ease; overflow: hidden;">
                        <input type="text" id="header-search" placeholder="{{ translateText('ناروغان، ډاکټران، راپورونه لټون کړئ...') }}" class="search-input" style="width: 300px; padding: 12px 16px; border: none; background: transparent; font-size: 14px; font-weight: 400; color: #374151; outline: none; transition: all 0.3s ease;">
                        <button onclick="performHeaderSearch()" class="search-btn" style="padding: 12px 18px; background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%); border: none; color: white; font-weight: 600; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; border-radius: 0 9px 9px 0; position: relative; overflow: hidden;">
                            <i class="fas fa-search text-sm"></i>
                            <span class="ml-2 text-sm font-medium hidden lg:inline">{{ translateText('لټون') }}</span>
                    
                            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 transform -skew-x-12 -translate-x-full transition-transform duration-500"></div>
                        </button>
                    </div> -->

                    <!-- Enhanced Search Results Dropdown -->
                    <div id="header-search-results" class="search-results absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-xl border border-gray-200 z-50 max-height-300 overflow-y-auto hidden" style="backdrop-filter: blur(20px);">
                        <!-- Results will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Right Side: Professional Action Buttons with Separation -->
            <div class="flex items-center space-x-6">
                <!-- Notifications Button removed -->

                <!-- New User Button -->
               <a href="{{ route('register') }}" class="action-btn new-user-btn" style="padding: 14px 18px; background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; border: none; border-radius: 12px; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 6px 16px rgba(16, 185, 129, 0.3); display: flex; align-items: center; gap: 10px; position: relative; overflow: hidden; margin-right: 8px; text-decoration: none;">
    <i class="fas fa-user-plus text-base"></i>
    <span class="hidden sm:inline font-medium">{{ translateText('نوي یوزر جوړول') }}</span>
    <!-- Professional hover effect -->
    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 transform -skew-x-12 -translate-x-full transition-transform duration-500"></div>
</a>

                <!-- Logout Button -->
                <form method="POST" action="{{ route('logout') }}" class="inline">
                    @csrf
                    <button type="submit" class="action-btn logout-btn" style="padding: 14px 18px; background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; border: none; border-radius: 12px; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 6px 16px rgba(239, 68, 68, 0.3); display: flex; align-items: center; gap: 10px; position: relative; overflow: hidden;" onclick="return confirm('آیا ډاډه یاست چې غواړئ وتل؟')">
                        <i class="fas fa-sign-out-alt text-base"></i>
                        <span class="hidden sm:inline font-medium">{{ translateText('خارجیدل') }}</span>
                        <!-- Professional hover effect -->
                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 transform -skew-x-12 -translate-x-full transition-transform duration-500"></div>
                    </button>
                </form>
            </div>
        </header>

        <!-- Page Content -->
        <div class="p-6 lg:p-8">
            @yield('contents')
        </div>
    </main>

    <!-- JavaScript -->
    <script>
        // Loading screen removed

        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const closeMenuBtn = document.getElementById('closeMenu');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');

        function openSidebar() {
            sidebar.classList.remove('translate-x-full');
            overlay.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeSidebar() {
            sidebar.classList.add('translate-x-full');
            overlay.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        mobileMenuBtn?.addEventListener('click', openSidebar);
        closeMenuBtn?.addEventListener('click', closeSidebar);
        overlay?.addEventListener('click', closeSidebar);

        // Active navigation state
        const currentPath = window.location.pathname;
        const navItems = document.querySelectorAll('.nav-item');

        navItems.forEach(item => {
            const href = item.getAttribute('href');
            if (href && currentPath.includes(href.split('/').pop())) {
                item.classList.add('bg-gradient-to-r', 'from-primary', 'to-secondary', 'text-white');
                item.classList.remove('text-gray-700');
            }
        });

        // Date and Time
        function updateDateTime() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            };

            const dateElement = document.getElementById('current-date');
            const timeElement = document.getElementById('current-time');

            if (dateElement) {
                dateElement.textContent = now.toLocaleDateString('fa-AF', options);
            }

            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString('fa-AF');
            }
        }

        // Update time every second
        updateDateTime();
        setInterval(updateDateTime, 1000);

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add hover effects to cards
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card, .summary-card, .chart-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.15)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
                });
            });
        });
    </script>
    <!-- Toast Notifications Container -->
    <div id="toast-container" class="fixed top-4 left-4 z-50 space-y-2"></div>

    <!-- Custom Styles for Enhanced UI -->
    <style>
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
        }

        /* Smooth transitions for all elements */
        * {
            transition: var(--transition);
        }

        /* Enhanced button styles */
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        /* Card animations */
        .card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        /* Loading animations removed */

        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        /* Notification styles removed */

        /* Enhanced form styles */
        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 12px 16px;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
        }

        /* Table styles */
        .table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .table th {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            font-weight: 600;
            color: #2d3748;
            padding: 16px;
            border: none;
        }

        .table td {
            padding: 16px;
            border-bottom: 1px solid #e2e8f0;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background: #f7fafc;
        }
    </style>

</body>

</html>

@section('scripts')
<script>
    // Notification system removed

    // Enhanced toast notification system
    function showToast(message, type = 'info', duration = 5000) {
        const container = document.getElementById('toast-container');
        const toast = document.createElement('div');

        const colors = {
            success: 'bg-green-500',
            error: 'bg-red-500',
            warning: 'bg-yellow-500',
            info: 'bg-blue-500'
        };

        toast.className = `notification-toast p-4 mb-2 text-white ${colors[type] || colors.info}`;
        toast.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} ml-2"></i>
                    <span>${message}</span>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" class="text-white hover:text-gray-200">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        container.appendChild(toast);

        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);

        // Auto-hide
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, duration);
    }

    // Initialize sidebar scrolling
    function initializeSidebarScrolling() {
        const sidebar = document.getElementById('sidebar');
        const sidebarNav = sidebar?.querySelector('nav');

        if (sidebarNav) {
            // Ensure proper scrolling behavior
            sidebarNav.style.overflowY = 'auto';
            sidebarNav.style.maxHeight = 'calc(100vh - 200px)'; // Account for header and footer

            // Add scroll event listener for visual feedback
            sidebarNav.addEventListener('scroll', function() {
                const scrollTop = this.scrollTop;
                const scrollHeight = this.scrollHeight;
                const clientHeight = this.clientHeight;

                // Add shadow at top when scrolled
                if (scrollTop > 10) {
                    this.style.boxShadow = 'inset 0 10px 10px -10px rgba(0,0,0,0.1)';
                } else {
                    this.style.boxShadow = 'none';
                }

                // Add shadow at bottom when not at bottom
                if (scrollTop + clientHeight < scrollHeight - 10) {
                    this.style.borderBottom = '1px solid rgba(0,0,0,0.1)';
                } else {
                    this.style.borderBottom = 'none';
                }
            });

            console.log('Sidebar scrolling initialized');
        }
    }

    // Notification functions removed

    function showNewUserModal() {
        // Create modal if it doesn't exist
        let modal = document.getElementById('new-user-modal');
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'new-user-modal';
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
            modal.innerHTML = `
                <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                    <div class="p-6 border-b border-gray-200 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-t-lg">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-semibold">نوی یوزر جوړول</h3>
                            <button onclick="closeNewUserModal()" class="text-white hover:text-gray-200">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <form id="new-user-form">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">نوم</label>
                                    <input type="text" name="name" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">ایمیل</label>
                                    <input type="email" name="email" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">پاسورډ</label>
                                    <input type="password" name="password" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">د یوزر ډول</label>
                                    <select name="role" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500">
                                        <option value="admin">ادمین</option>
                                        <option value="doctor">ډاکټر</option>
                                        <option value="user">عادي یوزر</option>
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="p-6 border-t border-gray-200 flex justify-end space-x-3 space-x-reverse bg-gray-50 rounded-b-lg">
                        <button onclick="closeNewUserModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">لغوه کول</button>
                        <button onclick="createNewUser()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">جوړول</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        modal.style.display = 'flex';
        modal.style.animation = 'fadeIn 0.3s ease';
    }

    function closeNewUserModal() {
        const modal = document.getElementById('new-user-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    function createNewUser() {
        const form = document.getElementById('new-user-form');
        showToast('نوی یوزر په بریالیتوب سره جوړ شو!', 'success');
        closeNewUserModal();
        form.reset();
    }

    // Enhanced sidebar active state management with blue color
    function setActiveNavigation() {
        const currentPath = window.location.pathname;
        const navItems = document.querySelectorAll('.nav-item');

        navItems.forEach(item => {
            const href = item.getAttribute('href');

            // Remove all active classes and reset styles first
            item.classList.remove('active', 'clicked');
            item.style.background = '';
            item.style.color = '';
            item.style.transform = '';
            item.style.boxShadow = '';

            // Check if this is the active page
            if (href && (currentPath === href || currentPath.includes(href.split('/').pop()))) {
                item.classList.add('active');

                // Apply blue styling for active page
                item.style.background = 'linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%)';
                item.style.color = 'white';
                item.style.transform = 'translateX(8px) scale(1.02)';
                item.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.4)';

                // Update icon color to white
                const icon = item.querySelector('i');
                if (icon) {
                    icon.style.color = 'white';
                }

                // Update icon container background
                const iconContainer = item.querySelector('.bg-blue-100, .bg-green-100, .bg-purple-100, .bg-orange-100, .bg-indigo-100, .bg-teal-100, .bg-yellow-100, .bg-red-100');
                if (iconContainer) {
                    iconContainer.style.background = 'rgba(255, 255, 255, 0.25)';
                    iconContainer.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
                }
            }
        });
    }

    // Enhanced click handlers for sidebar navigation with blue color
    function addNavClickHandlers() {
        const navItems = document.querySelectorAll('.nav-item');

        navItems.forEach(item => {
            item.addEventListener('click', function(e) {
                // Remove active state from all items
                navItems.forEach(navItem => {
                    navItem.classList.remove('active', 'clicked');
                    // Reset styles
                    navItem.style.background = '';
                    navItem.style.color = '';
                    navItem.style.transform = '';
                    navItem.style.boxShadow = '';

                    // Reset icon colors
                    const icon = navItem.querySelector('i');
                    if (icon) {
                        icon.style.color = '';
                    }

                    // Reset icon container
                    const iconContainer = navItem.querySelector('.bg-blue-100, .bg-green-100, .bg-purple-100, .bg-orange-100, .bg-indigo-100, .bg-teal-100, .bg-yellow-100, .bg-red-100');
                    if (iconContainer) {
                        iconContainer.style.background = '';
                    }
                });

                // Add active state to clicked item with blue color
                this.classList.add('active', 'clicked');

                // Apply blue styling
                this.style.background = 'linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%)';
                this.style.color = 'white';
                this.style.transform = 'translateX(8px) scale(1.02)';
                this.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.4)';

                // Update icon color to white
                const icon = this.querySelector('i');
                if (icon) {
                    icon.style.color = 'white';
                }

                // Update icon container background
                const iconContainer = this.querySelector('.bg-blue-100, .bg-green-100, .bg-purple-100, .bg-orange-100, .bg-indigo-100, .bg-teal-100, .bg-yellow-100, .bg-red-100');
                if (iconContainer) {
                    iconContainer.style.background = 'rgba(255, 255, 255, 0.25)';
                    iconContainer.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
                }
            });

            // Add hover effects
            item.addEventListener('mouseenter', function() {
                if (!this.classList.contains('active')) {
                    this.style.background = 'linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.1) 100%)';
                    this.style.transform = 'translateX(6px)';
                    this.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.1)';
                }
            });

            item.addEventListener('mouseleave', function() {
                if (!this.classList.contains('active')) {
                    this.style.background = '';
                    this.style.transform = '';
                    this.style.boxShadow = '';
                }
            });
        });
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Notification badge updates removed

        // Add fade-in animation to main content
        document.querySelector('main')?.classList.add('fade-in');

        // Initialize sidebar scrolling
        initializeSidebarScrolling();

        // Initialize sidebar navigation
        setActiveNavigation();
        addNavClickHandlers();

        // Show success/error messages as toasts
        @if(session('success'))
            showToast('{{ session('success') }}', 'success');
        @endif

        @if(session('error'))
            showToast('{{ session('error') }}', 'error');
        @endif
    });

    // Real-time notifications removed

    // Professional Header Search Functionality
    const headerSearchInput = document.getElementById('header-search');
    const headerSearchResults = document.getElementById('header-search-results');

    // Sample search data for header search
    const headerSearchData = [
        { type: 'patient', name: 'احمد علی', id: '001', category: 'ناروغ' },
        { type: 'patient', name: 'فاطمه خان', id: '002', category: 'ناروغ' },
        { type: 'doctor', name: 'ډاکټر محمد حسن', id: 'D001', category: 'ډاکټر' },
        { type: 'doctor', name: 'ډاکټر عایشه احمد', id: 'D002', category: 'ډاکټر' },
        { type: 'appointment', name: 'د نن ملاقاتونه', id: 'A001', category: 'ملاقات' },
        { type: 'report', name: 'میاشتنی راپور', id: 'R001', category: 'راپور' },
        { type: 'medication', name: 'د درملو لیست', id: 'M001', category: 'درمل' }
    ];

    // Header search input event listener
    if (headerSearchInput) {
        headerSearchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase().trim();

            if (query.length < 2) {
                headerSearchResults.classList.add('hidden');
                return;
            }

            const filteredResults = headerSearchData.filter(item =>
                item.name.toLowerCase().includes(query) ||
                item.category.toLowerCase().includes(query) ||
                item.id.toLowerCase().includes(query)
            );

            displayHeaderSearchResults(filteredResults);
        });

        // Header search on Enter key
        headerSearchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performHeaderSearch();
            }
        });
    }

    // Display header search results
    function displayHeaderSearchResults(results) {
        if (results.length === 0) {
            headerSearchResults.innerHTML = '<div class="search-result-item text-gray-500 text-center text-sm">هیڅ پایله ونه موندل شوه</div>';
        } else {
            headerSearchResults.innerHTML = results.map(item => `
                <div class="search-result-item" onclick="selectHeaderSearchResult('${item.type}', '${item.id}', '${item.name}')">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <i class="fas fa-${getIconForType(item.type)} text-gray-400 w-4"></i>
                            <div>
                                <div class="font-medium text-gray-900 text-sm">${item.name}</div>
                                <div class="text-xs text-gray-500">${item.category}</div>
                            </div>
                        </div>
                        <div class="text-xs text-gray-400">ID: ${item.id}</div>
                    </div>
                </div>
            `).join('');
        }

        headerSearchResults.classList.remove('hidden');
    }

    // Get icon for search result type
    function getIconForType(type) {
        const icons = {
            'patient': 'user',
            'doctor': 'user-md',
            'appointment': 'calendar',
            'report': 'chart-bar',
            'medication': 'pills'
        };
        return icons[type] || 'search';
    }

    // Select header search result
    function selectHeaderSearchResult(type, id, name) {
        headerSearchInput.value = name;
        headerSearchResults.classList.add('hidden');

        // Show notification
        showHeaderNotification(`${name} وټاکل شو`, 'success');

        // Navigate based on type (you can customize these routes)
        const routes = {
            'patient': '/patientd',
            'doctor': '/doctorcopy',
            'appointment': '/appointments',
            'report': '/reports',
            'medication': '/medications'
        };

        if (routes[type]) {
            // You can uncomment this to enable navigation
            // window.location.href = routes[type];
        }
    }

    // Perform header search function
    window.performHeaderSearch = function() {
        const query = headerSearchInput.value.trim();
        const searchBtn = document.querySelector('.search-btn');

        if (query) {
            // Direct search without loading effect
            console.log('Header search for:', query);
            // Here you would typically make an AJAX call to search
        } else {
            headerSearchInput.focus();
        }
    };

    // Header notification function
    function showHeaderNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-6 right-6 z-50 transform transition-all duration-500 cubic-bezier(0.4, 0, 0.2, 1)`;

        const colors = {
            success: 'from-green-500 to-emerald-600',
            error: 'from-red-500 to-rose-600',
            warning: 'from-yellow-500 to-orange-600',
            info: 'from-blue-500 to-indigo-600'
        };

        const icons = {
            success: 'check-circle',
            error: 'times-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };

        notification.innerHTML = `
            <div class="bg-gradient-to-r ${colors[type]} text-white px-6 py-4 rounded-xl shadow-2xl backdrop-filter backdrop-blur-sm border border-white border-opacity-20 max-w-sm">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-${icons[type]} text-lg"></i>
                    </div>
                    <div class="flex-1">
                        <p class="font-semibold text-sm leading-relaxed">${message}</p>
                    </div>
                    <button onclick="this.closest('.fixed').remove()" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            </div>
        `;

        // Initial position (off-screen)
        notification.style.transform = 'translateX(100%) translateY(-20px)';
        notification.style.opacity = '0';

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0) translateY(0)';
            notification.style.opacity = '1';
        }, 100);

        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%) translateY(-20px)';
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 500);
        }, 3000);
    }

    // Hide header search results when clicking outside
    document.addEventListener('click', function(e) {
        const searchContainer = document.querySelector('.search-container');
        if (searchContainer && !searchContainer.contains(e.target)) {
            headerSearchResults.classList.add('hidden');
        }
    });

    // Load notifications function removed

    // Notification dropdown functions removed

    // Notification helper functions removed

    // Update notification badge
    function updateNotificationBadge() {
        fetch('/api/notifications/count')
            .then(response => response.json())
            .then(data => {
                const badge = document.getElementById('notification-badge');
                if (badge) {
                    if (data.count > 0) {
                        badge.textContent = data.count;
                        badge.classList.remove('hidden');
                    } else {
                        badge.classList.add('hidden');
                    }
                }
            })
            .catch(error => {
                console.error('Error updating notification badge:', error);
            });
    }

    // Initialize notification badge on page load
    document.addEventListener('DOMContentLoaded', function() {
        updateNotificationBadge();
        // Update badge every 30 seconds
        setInterval(updateNotificationBadge, 30000);
    });
</script>

<!-- Notification sound removed --></audio>

<!-- Additional Scripts -->
<script src="{{ asset('js/csrf-refresh.js') }}"></script>
<script src="{{ asset('js/session-timeout.js') }}"></script>
@endsection



