  const contactForm = document.getElementById("contactForm");
    const nameRegex = /^[\u0600-\u06FF\s]{1,20}$/;
    const phoneRegex = /^07\d{8}$/;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (contactForm) {
      contactForm.addEventListener("submit", function(e) {
      e.preventDefault();
      let isValid = true;

      const fname = document.getElementById("fname").value.trim();
      const lname = document.getElementById("lname").value.trim();
      const email = document.getElementById("email").value.trim();
      const phone = document.getElementById("phone").value.trim();
      const message = document.getElementById("message").value.trim();

      clearErrors();

      if (!nameRegex.test(fname)) {
        showError("fname-error", "نوم باید یوازې توري وي او تر ۲۰ څخه کم");
        isValid = false;
      }
      if (!nameRegex.test(lname)) {
        showError("lname-error", "تخلص باید یوازې توري وي او تر ۲۰ څخه کم");
        isValid = false;
      }
      if (!emailRegex.test(email)) {
        showError("email-error", "برېښنالیک سم نه دی");
        isValid = false;
      }
      if (!phoneRegex.test(phone)) {
        showError("phone-error", "شمېره باید له 07 پیل شي او ۱۰ عدده وي");
        isValid = false;
      }
      if (message === "") {
        showError("message-error", "پیغام لازمي دی");
        isValid = false;
      }

      if (isValid) {
        alert("ستاسې پیغام په بریالیتوب واستول شو ✅");
        contactForm.reset();
      }
    });
  }

    function showError(id, msg) {
      document.getElementById(id).textContent = msg;
    }

    function clearErrors() {
      document.querySelectorAll(".error-message").forEach(el => el.textContent = "");
    }